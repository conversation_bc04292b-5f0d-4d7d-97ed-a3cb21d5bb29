@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* Advanced Parallax Animations */
@keyframes floatInSpace {
  0%, 100% {
    transform: translateY(0) translateZ(0) rotateY(0deg);
  }
  25% {
    transform: translateY(-20px) translateZ(50px) rotateY(5deg);
  }
  50% {
    transform: translateY(10px) translateZ(-30px) rotateY(-5deg);
  }
  75% {
    transform: translateY(-10px) translateZ(20px) rotateY(3deg);
  }
}

@keyframes perspectivePulse {
  0%, 100% {
    transform: perspective(1000px) translateZ(0) scale(1);
  }
  50% {
    transform: perspective(1000px) translateZ(100px) scale(1.1);
  }
}

@keyframes tunnelDive {
  0% {
    transform: perspective(500px) translateZ(-500px) rotateY(0deg) scale(0.5);
    opacity: 0;
  }
  100% {
    transform: perspective(500px) translateZ(0) rotateY(360deg) scale(1);
    opacity: 1;
  }
}

@keyframes matrixRain {
  0% {
    transform: translateY(-100vh) translateZ(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) translateZ(-200px);
    opacity: 0;
  }
}

@keyframes hologramGlitch {
  0%, 100% {
    transform: skew(0deg);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: skew(1deg) translateX(2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: skew(-1deg) translateX(-2px);
    filter: hue-rotate(180deg);
  }
}

@keyframes depthFloat {
  0% {
    transform: perspective(1200px) rotateX(20deg) rotateY(-20deg) translateZ(0);
  }
  50% {
    transform: perspective(1200px) rotateX(-20deg) rotateY(20deg) translateZ(100px);
  }
  100% {
    transform: perspective(1200px) rotateX(20deg) rotateY(-20deg) translateZ(0);
  }
}

/* Utility Classes */
.parallax-wrapper {
  perspective: 1px;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}

.parallax-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.parallax-layer-base {
  transform: translateZ(0);
}

.parallax-layer-back {
  transform: translateZ(-1px) scale(2);
}

.parallax-layer-deep {
  transform: translateZ(-2px) scale(3);
}

/* Fixed Screen Effect */
.fixed-screen {
  position: sticky;
  top: 0;
  height: 100vh;
  overflow: hidden;
}

/* 3D Scene Container */
.scene-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Depth Classes */
.depth-0 { transform: translateZ(0); }
.depth-1 { transform: translateZ(50px); }
.depth-2 { transform: translateZ(100px); }
.depth-3 { transform: translateZ(150px); }
.depth-minus-1 { transform: translateZ(-50px); }
.depth-minus-2 { transform: translateZ(-100px); }

/* Animations */
.float-in-space {
  animation: floatInSpace 10s ease-in-out infinite;
}

.perspective-pulse {
  animation: perspectivePulse 3s ease-in-out infinite;
}

.tunnel-dive {
  animation: tunnelDive 2s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.matrix-rain {
  animation: matrixRain 10s linear infinite;
}

.hologram-glitch {
  animation: hologramGlitch 0.5s ease-in-out infinite;
}

.depth-float {
  animation: depthFloat 15s ease-in-out infinite;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Responsive Typography Utilities */
  .text-responsive-display {
    @apply text-display-1-mobile sm:text-display-1;
  }
  
  .text-responsive-h1 {
    @apply text-h1-mobile sm:text-h1;
  }
  
  .text-responsive-h2 {
    @apply text-h2-mobile sm:text-h2;
  }
  
  .text-responsive-body-lg {
    @apply text-base sm:text-body-lg;
  }
  
  .text-responsive-body {
    @apply text-sm sm:text-body-md;
  }
  
  /* Mobile-optimized spacing utilities */
  .space-y-mobile {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }
  
  .gap-mobile {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }
  
  .p-mobile {
    @apply p-4 sm:p-6 lg:p-8;
  }
  
  .px-mobile {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .py-mobile {
    @apply py-4 sm:py-6 lg:py-8;
  }
  
  /* Mobile touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px];
  }
  
  /* Mobile animation optimization */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Optimize animations for mobile performance */
  @media (max-width: 640px) {
    .motion-safe {
      animation-duration: 0.3s;
      transition-duration: 0.2s;
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
