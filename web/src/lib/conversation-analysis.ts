// TinderOP Helper - Conversation Analysis Service
// Frontend service for analyzing dating app conversations via Chrome extension

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";

// Types for conversation analysis
export interface ConversationContext {
  platform: "tinder" | "bumble" | "hinge" | "unknown";
  url: string;
  timestamp: number;
  messages: string[];
  userAgent?: string;
}

export interface ConversationSuggestion {
  text: string;
  tone: "witty" | "sincere" | "flirty" | "casual" | "thoughtful";
  confidence: number;
  reasoning?: string;
}

export interface ConversationAnalysisResult {
  suggestions: ConversationSuggestion[];
  analysis: {
    conversationTone: string;
    userPersonality: string;
    recommendedApproach: string;
    confidence: number;
  };
  processingTime: number;
  platform: string;
  timestamp: number;
  success: boolean;
  error?: string;
}

export interface ConversationAnalysisProgress {
  phase: "capture" | "upload" | "analysis" | "suggestions" | "complete";
  progress: number; // 0-100
  message: string;
}

export interface ConversationAnalysisConfig {
  onProgress?: (progress: ConversationAnalysisProgress) => void;
  onComplete?: (result: ConversationAnalysisResult) => void;
  onError?: (error: string) => void;
  apiUrl?: string;
  maxRetries?: number;
  timeout?: number;
}

export class ConversationAnalysisService {
  private isAnalyzing = false;
  private apiKey: string | null = null;

  constructor() {
    // Get API key from environment
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    if (!this.apiKey) {
      console.warn(
        "⚠️ OpenRouter API key not found. Conversation analysis will use API endpoint fallback."
      );
    }

    console.log("💬 ConversationAnalysisService initialized");
  }

  async analyzeConversation(
    screenshot: string, // Base64 encoded
    context: ConversationContext,
    config: ConversationAnalysisConfig = {}
  ): Promise<ConversationAnalysisResult> {
    if (this.isAnalyzing) {
      throw new Error("Conversation analysis already in progress");
    }

    this.isAnalyzing = true;
    const startTime = Date.now();

    try {
      console.log(`🔍 Starting conversation analysis for ${context.platform}`);

      // Phase 1: Validate inputs
      config.onProgress?.({
        phase: "capture",
        progress: 10,
        message: "Validating screenshot and context...",
      });

      this.validateInputs(screenshot, context);

      // Phase 2: Choose analysis method
      config.onProgress?.({
        phase: "upload",
        progress: 25,
        message: "Preparing analysis...",
      });

      let result: ConversationAnalysisResult;

      if (this.shouldUseDirectAI()) {
        // Use direct AI analysis (client-side)
        result = await this.analyzeDirectly(screenshot, context, config);
      } else {
        // Use API endpoint (server-side)
        result = await this.analyzeViaAPI(screenshot, context, config);
      }

      config.onProgress?.({
        phase: "complete",
        progress: 100,
        message: "Analysis complete!",
      });

      config.onComplete?.(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Conversation analysis failed:", errorMessage);

      config.onError?.(errorMessage);

      return {
        suggestions: this.getFallbackSuggestions(context),
        analysis: {
          conversationTone: "unknown",
          userPersonality: "unknown",
          recommendedApproach: "engage naturally",
          confidence: 0.3,
        },
        processingTime: Date.now() - startTime,
        platform: context.platform,
        timestamp: Date.now(),
        success: false,
        error: errorMessage,
      };
    } finally {
      this.isAnalyzing = false;
    }
  }

  private validateInputs(screenshot: string, context: ConversationContext): void {
    if (!screenshot || typeof screenshot !== "string") {
      throw new Error("Valid screenshot is required");
    }

    if (!context || typeof context !== "object") {
      throw new Error("Valid context is required");
    }

    if (!this.isValidBase64(screenshot)) {
      throw new Error("Screenshot must be valid base64 encoded image");
    }
  }

  private isValidBase64(str: string): boolean {
    try {
      return btoa(atob(str)) === str;
    } catch {
      return false;
    }
  }

  private shouldUseDirectAI(): boolean {
    // Use direct AI if we have an API key and we're in a context that supports it
    return !!(this.apiKey && typeof window !== "undefined");
  }

  private async analyzeDirectly(
    screenshot: string,
    context: ConversationContext,
    config: ConversationAnalysisConfig
  ): Promise<ConversationAnalysisResult> {
    const startTime = Date.now();

    config.onProgress?.({
      phase: "analysis",
      progress: 50,
      message: "Analyzing conversation with AI...",
    });

    try {
      const prompt = this.createAnalysisPrompt(context);

      const result = await generateText({
        model: openrouter("google/gemini-2.0-flash-exp:free"),
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image",
                image: `data:image/png;base64,${screenshot}`,
              },
            ],
          },
        ],
        temperature: 0.7,
        maxTokens: 1000,
      });

      config.onProgress?.({
        phase: "suggestions",
        progress: 80,
        message: "Generating suggestions...",
      });

      const suggestions = this.parseAIResponse(result.text);
      const analysis = this.extractAnalysis(result.text, context);

      return {
        suggestions,
        analysis,
        processingTime: Date.now() - startTime,
        platform: context.platform,
        timestamp: Date.now(),
        success: true,
      };
    } catch (error) {
      console.error("❌ Direct AI analysis failed:", error);
      throw error;
    }
  }

  private async analyzeViaAPI(
    screenshot: string,
    context: ConversationContext,
    config: ConversationAnalysisConfig
  ): Promise<ConversationAnalysisResult> {
    const startTime = Date.now();
    const apiUrl = config.apiUrl || "http://localhost:5173";

    config.onProgress?.({
      phase: "upload",
      progress: 40,
      message: "Sending to analysis API...",
    });

    try {
      const response = await fetch(`${apiUrl}/api/tinder-helper/analyze`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          screenshot,
          context,
          timestamp: Date.now(),
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      config.onProgress?.({
        phase: "analysis",
        progress: 70,
        message: "Processing analysis results...",
      });

      const apiResult = await response.json();

      if (!apiResult.success) {
        throw new Error(apiResult.error || "API analysis failed");
      }

      return {
        suggestions: apiResult.suggestions || [],
        analysis: apiResult.analysis || {
          conversationTone: "unknown",
          userPersonality: "unknown",
          recommendedApproach: "engage naturally",
          confidence: 0.5,
        },
        processingTime: Date.now() - startTime,
        platform: context.platform,
        timestamp: Date.now(),
        success: true,
      };
    } catch (error) {
      console.error("❌ API analysis failed:", error);
      throw error;
    }
  }

  private createAnalysisPrompt(context: ConversationContext): string {
    const { platform, messages } = context;

    return `You are a BRUTALLY HONEST dating conversation expert. Most conversations are boring and fail - be critical and realistic about what actually works.

Recent conversation context:
${messages && messages.length > 0 ? messages.join("\n") : "No text context available - analyze the screenshot."}

CRITICAL ANALYSIS REQUIREMENTS:
1. BE RUTHLESSLY HONEST about conversation quality and engagement level
2. Most conversations are mediocre - don't sugarcoat poor communication
3. Identify conversation killers, boring responses, and missed opportunities
4. Only suggest replies that would ACTUALLY stand out and create attraction

HARSH REALITY CHECK:
- Most people send boring, generic messages that get ignored
- Confidence scores should reflect REAL likelihood of success (most are 0.3-0.6)
- Bad conversations deserve harsh criticism
- Only exceptional suggestions deserve high confidence

Generate 3-4 reply options with REALISTIC assessment:
- One witty/humorous response (if the conversation actually allows for it)
- One sincere/genuine response (that isn't boring)
- One flirty/playful response (only if appropriate and not creepy)
- One thoughtful/question-based response (that isn't generic)

Each suggestion must be:
- Actually engaging (not generic small talk)
- Likely to stand out from other matches
- Under 200 characters
- Platform-appropriate for ${platform}
- REALISTIC about success probability

BRUTAL ANALYSIS REQUIREMENTS:
- Harshly assess conversation quality and engagement
- Identify why most responses would fail
- Be honest about the other person's interest level
- Provide realistic confidence scores (0.1-0.9 range)

Respond in this exact JSON format:
{
  "suggestions": [
    {
      "text": "Your reply suggestion here",
      "tone": "witty|sincere|flirty|thoughtful",
      "confidence": 0.45,
      "reasoning": "Honest assessment of why this might work or fail"
    }
  ],
  "analysis": {
    "conversationTone": "boring|engaging|flirty|dying|etc",
    "userPersonality": "Harsh but accurate personality assessment",
    "recommendedApproach": "Realistic strategy (including if conversation is already dead)",
    "confidence": 0.35,
    "harshReality": "Brutal truth about conversation quality and prospects"
  }
}

Be brutally honest - most conversations are failing and need harsh truth, not false hope.`;
  }

  private parseAIResponse(aiResponse: string): ConversationSuggestion[] {
    try {
      const cleaned = aiResponse.replace(/```json\n?|```\n?/g, "").trim();
      const parsed = JSON.parse(cleaned);

      console.log("💬 Conversation Analysis Results:", {
        conversationTone: parsed.analysis?.conversationTone,
        confidence: parsed.analysis?.confidence,
        harshReality: parsed.analysis?.harshReality,
        suggestionsCount: parsed.suggestions?.length || 0,
        avgConfidence:
          parsed.suggestions?.reduce((sum: number, s: any) => sum + (s.confidence || 0), 0) /
          (parsed.suggestions?.length || 1),
      });

      if (parsed.suggestions && Array.isArray(parsed.suggestions)) {
        return parsed.suggestions.map((s: any) => ({
          text: s.text || "",
          tone: s.tone || "casual",
          confidence: s.confidence || 0.4, // Lower default confidence
          reasoning: s.reasoning || "",
        }));
      }
    } catch (error) {
      console.warn("⚠️ Failed to parse AI response as JSON, using fallback");
    }

    return this.extractSuggestionsFromText(aiResponse);
  }

  private extractSuggestionsFromText(text: string): ConversationSuggestion[] {
    const suggestions: ConversationSuggestion[] = [];

    const patterns = [
      /(?:suggestion|option|reply)[\s\d:.-]*(.+?)(?:\n|$)/gi,
      /"([^"]+)"/g,
      /^\d+\.\s*(.+?)(?:\n|$)/gm,
    ];

    for (const pattern of patterns) {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        const suggestion = match[1]?.trim();
        if (suggestion && suggestion.length > 10 && suggestion.length < 300) {
          suggestions.push({
            text: suggestion,
            tone: "casual",
            confidence: 0.6,
          });

          if (suggestions.length >= 4) break;
        }
      }
      if (suggestions.length >= 4) break;
    }

    return suggestions.slice(0, 4);
  }

  private extractAnalysis(aiResponse: string, context: ConversationContext): any {
    try {
      const cleaned = aiResponse.replace(/```json\n?|```\n?/g, "").trim();
      const parsed = JSON.parse(cleaned);

      if (parsed.analysis) {
        return parsed.analysis;
      }
    } catch (error) {
      console.warn("⚠️ Failed to parse analysis from AI response");
    }

    return {
      conversationTone: "unclear",
      userPersonality: "difficult to assess",
      recommendedApproach: "proceed with caution",
      confidence: 0.3,
      harshReality: "Unable to properly analyze conversation quality",
    };
  }

  private getFallbackSuggestions(context: ConversationContext): ConversationSuggestion[] {
    const platform = context.platform;

    const baseSuggestions = [
      {
        text: "That's interesting - tell me more about that",
        tone: "sincere" as const,
        confidence: 0.4,
        reasoning: "Generic but safe fallback response",
      },
      {
        text: "What's something you're passionate about?",
        tone: "thoughtful" as const,
        confidence: 0.3,
        reasoning: "Standard question - low engagement potential",
      },
      {
        text: "How's your day going?",
        tone: "casual" as const,
        confidence: 0.2,
        reasoning: "Basic small talk - likely to be ignored",
      },
    ];

    // Add platform-specific suggestion
    if (platform === "tinder") {
      baseSuggestions.push({
        text: "Your photos are really great! What's the story behind that adventure?",
        tone: "sincere" as const,
        confidence: 0.5,
        reasoning: "Photo-focused opener for visual-first platform",
      });
    } else if (platform === "bumble") {
      baseSuggestions.push({
        text: "I noticed we have [common interest]. How did you get into that?",
        tone: "casual" as const,
        confidence: 0.6,
        reasoning: "Interest-based conversation starter for professional platform",
      });
    } else if (platform === "hinge") {
      baseSuggestions.push({
        text: "Your prompt about [topic] caught my attention. What's your take on that?",
        tone: "thoughtful" as const,
        confidence: 0.7,
        reasoning: "Prompt-focused approach for relationship-oriented platform",
      });
    }

    return baseSuggestions;
  }

  // Utility methods
  isCurrentlyAnalyzing(): boolean {
    return this.isAnalyzing;
  }

  // Test API connectivity
  async testConnection(apiUrl: string = "http://localhost:5173"): Promise<boolean> {
    try {
      const response = await fetch(`${apiUrl}/api/health`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      return response.ok;
    } catch (error) {
      console.error("❌ Connection test failed:", error);
      return false;
    }
  }
}

// Export singleton instance
export const conversationAnalysisService = new ConversationAnalysisService();
