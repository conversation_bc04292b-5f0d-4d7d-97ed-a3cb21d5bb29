import type {
  AnalysisProgress,
  AnalysisR<PERSON>ult,
  BioAnalysisProgress,
  BioAnalysisResult,
  StepResult,
} from "@/types/analysis";
import { bioAnalysisAgent } from "./bio-analysis";
import { imageAnalysisAgent } from "./image-analysis";
import { convertBlobToBase64, type StoredImage } from "./storage";

export interface AnalysisServiceConfig {
  onProgress?: (progress: AnalysisProgress) => void;
  onStepComplete?: (stepResult: StepResult) => void;
  onComplete?: (result: AnalysisResult) => void;
  onError?: (error: string) => void;
}

export interface BioAnalysisServiceConfig {
  onProgress?: (progress: BioAnalysisProgress) => void;
  onStepComplete?: (stepResult: StepResult) => void;
  onComplete?: (result: BioAnalysisResult) => void;
  onError?: (error: string) => void;
}

export class AnalysisService {
  private isAnalyzing = false;
  private isBioAnalyzing = false;

  async analyzeImage(
    storedImage: StoredImage,
    config: AnalysisServiceConfig = {}
  ): Promise<AnalysisResult> {
    if (this.isAnalyzing) {
      throw new Error("Analysis already in progress");
    }

    this.isAnalyzing = true;

    try {
      // Convert blob to base64 for AI analysis
      const imageBase64 = await convertBlobToBase64(storedImage.blob);

      const result: AnalysisResult = {
        fileName: storedImage.fileName,
        preview: URL.createObjectURL(storedImage.blob),
        overallScore: 0,
        steps: [],
        recommendations: [],
        processed: false,
      };

      // Execute analysis with progress tracking
      const stepResults = await imageAnalysisAgent.analyzeImage(
        imageBase64,
        storedImage.fileName,
        (stepId, stepName, progress) => {
          const progressData: AnalysisProgress = {
            fileName: storedImage.fileName,
            currentStep: stepId,
            totalSteps: 5,
            stepName,
            progress,
          };
          config.onProgress?.(progressData);
        }
      );

      // Process each step result
      for (const stepResult of stepResults) {
        result.steps.push(stepResult);
        config.onStepComplete?.(stepResult);
      }

      // Calculate overall score and recommendations
      result.overallScore = imageAnalysisAgent.calculateOverallScore(stepResults);
      result.recommendations = imageAnalysisAgent.generateFinalRecommendations(stepResults);
      result.processed = true;

      // Cleanup: Clear the base64 data from memory
      // The base64 string will be garbage collected

      config.onComplete?.(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      config.onError?.(errorMessage);

      return {
        fileName: storedImage.fileName,
        preview: URL.createObjectURL(storedImage.blob),
        overallScore: 0,
        steps: [],
        recommendations: ["Analysis failed. Please try again."],
        processed: false,
        error: errorMessage,
      };
    } finally {
      this.isAnalyzing = false;
    }
  }

  async analyzeBatch(
    storedImages: StoredImage[],
    config: AnalysisServiceConfig = {}
  ): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = [];

    for (let i = 0; i < storedImages.length; i++) {
      const image = storedImages[i];

      try {
        const result = await this.analyzeImage(image, {
          ...config,
          onProgress: (progress) => {
            // Adjust progress to account for batch processing
            const batchProgress = {
              ...progress,
              progress: (i / storedImages.length) * 100 + progress.progress / storedImages.length,
            };
            config.onProgress?.(batchProgress);
          },
        });

        results.push(result);

        // Add small delay between analyses to prevent overwhelming the API
        if (i < storedImages.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Failed to analyze ${image.fileName}:`, error);
        results.push({
          fileName: image.fileName,
          preview: URL.createObjectURL(image.blob),
          overallScore: 0,
          steps: [],
          recommendations: ["Analysis failed for this image."],
          processed: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  }

  async analyzeBio(
    bio: string,
    tone: "witty" | "sincere" | "adventurous" = "sincere",
    config: BioAnalysisServiceConfig = {}
  ): Promise<BioAnalysisResult> {
    if (this.isBioAnalyzing) {
      throw new Error("Bio analysis already in progress");
    }

    this.isBioAnalyzing = true;

    try {
      const result: BioAnalysisResult = {
        originalBio: bio,
        overallScore: 0,
        steps: [],
        recommendations: [],
        processed: false,
      };

      // Execute analysis with progress tracking
      const stepResults = await bioAnalysisAgent.analyzeBio(bio, (stepId, stepName, progress) => {
        const progressData: BioAnalysisProgress = {
          currentStep: stepId,
          totalSteps: 5,
          stepName,
          progress,
        };
        config.onProgress?.(progressData);
      });

      // Process each step result
      for (const stepResult of stepResults) {
        result.steps.push(stepResult);
        config.onStepComplete?.(stepResult);
      }

      // Calculate overall score and recommendations
      result.overallScore = bioAnalysisAgent.calculateOverallScore(stepResults);
      result.recommendations = bioAnalysisAgent.generateFinalRecommendations(stepResults);

      // Generate improved bio if score is low enough to warrant it
      if (result.overallScore < 75) {
        try {
          result.improvedBio = await bioAnalysisAgent.generateImprovedBio(bio, stepResults, tone);
        } catch (error) {
          console.error("Failed to generate improved bio:", error);
        }
      }

      result.processed = true;

      config.onComplete?.(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      config.onError?.(errorMessage);

      return {
        originalBio: bio,
        overallScore: 0,
        steps: [],
        recommendations: ["Analysis failed. Please try again."],
        processed: false,
        error: errorMessage,
      };
    } finally {
      this.isBioAnalyzing = false;
    }
  }

  isCurrentlyAnalyzing(): boolean {
    return this.isAnalyzing;
  }

  isCurrentlyAnalyzingBio(): boolean {
    return this.isBioAnalyzing;
  }
}

// Singleton instance
export const analysisService = new AnalysisService();

// Utility function to simulate streaming for demo purposes
export function createAnalysisStream(
  storedImage: StoredImage,
  onEvent: (event: { type: string; data: any }) => void
): Promise<AnalysisResult> {
  return analysisService.analyzeImage(storedImage, {
    onProgress: (progress) => {
      onEvent({ type: "progress", data: progress });
    },
    onStepComplete: (stepResult) => {
      onEvent({ type: "step-complete", data: stepResult });
    },
    onComplete: (result) => {
      onEvent({ type: "analysis-complete", data: result });
    },
    onError: (error) => {
      onEvent({ type: "error", data: { error } });
    },
  });
}
