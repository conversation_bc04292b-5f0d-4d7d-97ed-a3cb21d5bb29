// Advanced prompting system for image analysis with o3

import { type ExpertPersona, getExpertPersona } from "../types/expert-personas";

export interface PromptTemplate {
  systemPrompt: string;
  userPrompt: string;
  chainOfThoughtStructure: string;
  examples?: string[];
}

export class AdvancedImagePromptGenerator {
  /**
   * Generate expert-specific prompt for image analysis
   */
  generateExpertPrompt(expertType: string, analysisContext?: any): PromptTemplate {
    const persona = getExpertPersona(expertType);

    const systemPrompt = this.buildSystemPrompt(persona);
    const userPrompt = this.buildUserPrompt(persona, analysisContext);
    const chainOfThoughtStructure = this.buildChainOfThoughtStructure(persona);

    return {
      systemPrompt,
      userPrompt,
      chainOfThoughtStructure,
      examples: this.getExamples(expertType),
    };
  }

  /**
   * Build comprehensive system prompt with expert persona
   */
  private buildSystemPrompt(persona: ExpertPersona): string {
    return `You are ${persona.name}, ${persona.credentials}.

BACKGROUND & EXPERTISE:
${persona.background}

Your core expertise includes:
${persona.expertise.map((item) => `• ${item}`).join("\n")}

ANALYSIS APPROACH:
${persona.analysisApproach}

CRITICAL ANALYSIS REQUIREMENTS:
You are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating profile images, near-perfect execution
- 80-89: EXCELLENT - Top 15% of images, very strong with minor flaws
- 70-79: GOOD - Above average, solid but with notable improvement areas
- 60-69: AVERAGE - Typical image, significant room for improvement
- 50-59: BELOW AVERAGE - Multiple issues, needs major work
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, would repel most matches
- 20-29: TERRIBLE - Fundamentally broken, needs complete reshoot
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used under any circumstances

ANALYSIS REQUIREMENTS:
1. BE RUTHLESSLY CRITICAL - Most images are mediocre and deserve low scores
2. IDENTIFY EVERY FLAW - No matter how small, call out problems
3. DEMAND EXCELLENCE - Only exceptional images deserve high scores
4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good
5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles

RESPONSE REQUIREMENTS:
- Be uncompromisingly honest about weaknesses
- Support every criticism with specific visual evidence
- Score based on real dating market performance
- Prioritize brutal honesty over politeness
- Remember: Average images get average results (poor performance)

Your analysis will be combined with other experts to provide comprehensive feedback.`;
  }

  /**
   * Build user prompt with chain-of-thought structure
   */
  private buildUserPrompt(persona: ExpertPersona, context?: any): string {
    const contextInfo = context ? this.buildContextInfo(context) : "";

    return `${contextInfo}

Please analyze this dating profile image using your expertise as a ${persona.type} expert.

ANALYSIS FRAMEWORK:
Follow this systematic approach:

1. INITIAL OBSERVATION PHASE:
   - What are the key visual elements I observe?
   - What stands out immediately from my expert perspective?
   - What technical/aesthetic/psychological factors are present?

2. DETAILED EXPERT ANALYSIS:
   - What specific improvements are needed from my expert perspective?
   - What concrete changes would optimize this for dating success?
   - How can this be enhanced to follow best practices in my field?
   - Focus on ACTIONABLE IMPROVEMENTS, not just observations

3. CRITICAL SCORING RATIONALE:
   - What score (0-100) does this image ACTUALLY deserve? (Be harsh - most images are 40-60)
   - What SPECIFIC FLAWS and weaknesses lower this score?
   - Why would this image FAIL in the competitive dating market?
   - What evidence PROVES this score is accurate?

4. CONFIDENCE ASSESSMENT:
   - How confident am I in this analysis (0-100)?
   - What factors increase or decrease my confidence?
   - What additional information would improve my assessment?

5. ACTIONABLE RECOMMENDATIONS:
   - What are the top 3-5 most impactful improvements needed?
   - Which changes would have the highest impact on dating success?
   - What is the effort level required for each improvement?
   - Be specific about HOW to implement each improvement

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "observation_phase": {
    "key_elements": ["element1", "element2", "element3"],
    "immediate_impressions": "Your first impressions as an expert",
    "technical_factors": ["factor1", "factor2"]
  },
  "expert_evaluation": {
    "expert_specific_analysis": "Focus on SPECIFIC IMPROVEMENTS needed, not just observations. What exactly should be changed, improved, or optimized? Provide actionable insights.",
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"]
  },
  "scoring_methodology": {
    "score": 45,
    "harsh_reality_check": "Why this image would struggle in the real dating market",
    "critical_flaws": ["major_visual_flaw1", "major_flaw2", "major_flaw3"],
    "evidence_for_low_score": ["specific_visual_evidence1", "specific_evidence2"],
    "market_performance_prediction": "How this would actually perform (be realistic)"
  },
  "confidence_evaluation": {
    "confidence": 92,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["area1", "area2"]
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement advice",
      "impact_level": "high",
      "implementation_difficulty": "easy",
      "reasoning": "Why this improvement matters and how it helps"
    },
    {
      "recommendation": "Another specific improvement",
      "impact_level": "medium",
      "implementation_difficulty": "moderate",
      "reasoning": "Detailed reasoning for this improvement"
    }
  ]
}

Remember: Your analysis should reflect your specific expertise while being constructive and actionable.`;
  }

  /**
   * Build chain-of-thought structure for the expert
   */
  private buildChainOfThoughtStructure(persona: ExpertPersona): string {
    return `
CHAIN-OF-THOUGHT REASONING STRUCTURE:

Step 1: Initial Expert Observation
- "As a ${persona.type} expert, I immediately notice..."
- "The key elements that stand out to me are..."
- "From my professional perspective, this image shows..."

Step 2: Technical/Professional Analysis
- "Applying my expertise in ${persona.expertise[0]}, I can see..."
- "The technical quality/psychological factors/style elements indicate..."
- "Based on my experience with ${persona.specializations[0]}, this demonstrates..."

Step 3: Comparative Assessment
- "Compared to successful profiles I've analyzed..."
- "This ranks in the [percentile] of images I've evaluated because..."
- "The market positioning would be..."

Step 4: Evidence-Based Scoring
- "I'm scoring this [X]/100 because..."
- "The evidence supporting this score includes..."
- "The main factors influencing this score are..."

Step 5: Strategic Recommendations
- "The highest impact improvement would be..."
- "Based on my expertise, I recommend..."
- "The priority order for improvements should be..."
`;
  }

  /**
   * Build context information for the analysis
   */
  private buildContextInfo(context: any): string {
    let contextStr = "ANALYSIS CONTEXT:\n";

    if (context.targetDemographic) {
      contextStr += `• Target Demographic: ${context.targetDemographic}\n`;
    }

    if (context.platform) {
      contextStr += `• Platform: ${context.platform}\n`;
    }

    if (context.analysisDepth) {
      contextStr += `• Analysis Depth: ${context.analysisDepth}\n`;
    }

    return contextStr + "\n";
  }

  /**
   * Get few-shot examples for the expert type
   */
  private getExamples(expertType: string): string[] {
    const examples: Record<string, string[]> = {
      photography: [
        `Example: "As a photography expert, I immediately notice the harsh overhead lighting creating unflattering shadows under the eyes and nose. The composition is off-center without artistic intent, and the background is cluttered with distracting elements. Score: 23/100 because this amateur photography actively hurts the subject's appeal and screams 'low effort selfie.'"`,

        `Example: "This image demonstrates decent use of natural window light and acceptable composition. However, the lighting could be more flattering, the background lacks visual interest, and the pose appears stiff. Score: 67/100 - while technically competent, it lacks the polish needed to stand out in today's competitive dating market."`,
      ],

      psychology: [
        `Example: "From a psychological perspective, while the subject attempts a smile, it lacks genuine Duchenne markers and appears practiced rather than spontaneous. The eye contact is adequate but not particularly engaging. Body language shows some tension. Score: 54/100 because these psychological cues suggest someone trying to appear confident rather than naturally being so."`,

        `Example: "The facial expression is clearly forced, with visible tension in the jaw and a smile that screams 'fake.' The averted gaze signals insecurity and discomfort. Body language appears defensive. Score: 19/100 because these psychological red flags would actively repel potential matches who can sense inauthenticity."`,
      ],

      fashion: [
        `Example: "The styling shows basic understanding of color coordination with the navy shirt working adequately with the subject's skin tone. However, the fit is standard rather than tailored, and the overall look lacks sophistication or personality. Score: 58/100 - while not offensive, it's forgettable and doesn't create any visual impact."`,

        `Example: "The outfit choice is a complete disaster - the oversized graphic tee looks juvenile and sloppy, the colors clash horribly with the subject's complexion, and the overall styling suggests someone who doesn't understand basic fashion principles. Score: 14/100 because this actively damages the subject's attractiveness and signals poor judgment."`,
      ],

      data_science: [
        `Example: "Based on platform analytics, this image type shows moderate performance indicators but lacks the standout elements that drive top-tier engagement. While technically adequate, it falls into the oversaturated 'decent but forgettable' category. Score: 52/100 based on competitive analysis showing this style performs below median in current market conditions."`,

        `Example: "This image style (poor lighting, forced expression, cluttered background) correlates with bottom 15% performance metrics. Platform algorithms actively deprioritize such images, leading to 73% fewer profile views. Score: 11/100 based on conversion rate analysis showing this type generates virtually zero quality matches."`,
      ],

      dating_coach: [
        `Example: "This image shows decent approachability but lacks the magnetic confidence needed for standout dating success. The expression is pleasant but forgettable, and the setting provides minimal conversation value. Score: 59/100 because while it won't actively hurt, it won't generate the excitement needed in today's competitive dating market."`,

        `Example: "While the subject may be physically attractive, this image is a dating disaster. The bland expression, generic setting, and complete lack of personality make it instantly forgettable. Score: 31/100 because it would generate few matches and even fewer meaningful conversations - a waste of potential."`,
      ],
    };

    return examples[expertType] || [];
  }
}
