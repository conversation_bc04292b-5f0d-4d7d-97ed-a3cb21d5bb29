// Advanced prompting system for bio analysis with o3

import { type ExpertPersona, getExpertPersona } from "../types/expert-personas";

export interface BioPromptTemplate {
  systemPrompt: string;
  userPrompt: string;
  chainOfThoughtStructure: string;
  examples?: string[];
}

export class AdvancedBioPromptGenerator {
  /**
   * Generate expert-specific prompt for bio analysis
   */
  generateExpertPrompt(expertType: string, bio: string, analysisContext?: any): BioPromptTemplate {
    const persona = getExpertPersona(expertType);

    const systemPrompt = this.buildSystemPrompt(persona);
    const userPrompt = this.buildUserPrompt(persona, bio, analysisContext);
    const chainOfThoughtStructure = this.buildChainOfThoughtStructure(persona);

    return {
      systemPrompt,
      userPrompt,
      chainOfThoughtStructure,
      examples: this.getExamples(expertType),
    };
  }

  /**
   * Build comprehensive system prompt with expert persona
   */
  private buildSystemPrompt(persona: ExpertPersona): string {
    return `You are ${persona.name}, ${persona.credentials}.

BACKGROUND & EXPERTISE:
${persona.background}

Your core expertise includes:
${persona.expertise.map((item) => `• ${item}`).join("\n")}

ANALYSIS APPROACH:
${persona.analysisApproach}

CRITICAL ANALYSIS REQUIREMENTS:
You are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating profiles, near-perfect execution
- 80-89: EXCELLENT - Top 15% of profiles, very strong with minor flaws
- 70-79: GOOD - Above average, solid but with notable improvement areas
- 60-69: AVERAGE - Typical profile, significant room for improvement
- 50-59: BELOW AVERAGE - Multiple issues, needs major work
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, would repel most matches
- 20-29: TERRIBLE - Fundamentally broken, needs complete rewrite
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used under any circumstances

ANALYSIS REQUIREMENTS:
1. BE RUTHLESSLY CRITICAL - Most bios are mediocre and deserve low scores
2. IDENTIFY EVERY FLAW - No matter how small, call out problems
3. DEMAND EXCELLENCE - Only exceptional bios deserve high scores
4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good
5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles

RESPONSE REQUIREMENTS:
- Be uncompromisingly honest about weaknesses
- Support every criticism with specific evidence
- Score based on real dating market performance
- Prioritize brutal honesty over politeness
- Remember: Average bios get average results (poor performance)

Your analysis will be synthesized with other expert perspectives for comprehensive feedback.`;
  }

  /**
   * Build user prompt with chain-of-thought structure
   */
  private buildUserPrompt(persona: ExpertPersona, bio: string, context?: any): string {
    const contextInfo = context ? this.buildContextInfo(context) : "";

    return `${contextInfo}

Please analyze this dating profile bio using your expertise as a ${persona.type} expert.

BIO TO ANALYZE:
"${bio}"

SYSTEMATIC ANALYSIS FRAMEWORK:

1. INITIAL ASSESSMENT PHASE:
   - What is my immediate impression of this bio?
   - What key elements stand out from my expert perspective?
   - What does this bio communicate about the person?

2. DETAILED EXPERT EVALUATION:
   - How does this bio perform in my area of expertise?
   - What specific strengths and weaknesses do I identify?
   - How does this compare to successful bios I've analyzed?

3. PSYCHOLOGICAL/LINGUISTIC/MARKET ANALYSIS:
   - What personality traits or characteristics are evident?
   - How effective is the communication style?
   - What market positioning does this create?

4. CRITICAL SCORING METHODOLOGY:
   - What score (0-100) does this bio ACTUALLY deserve? (Be harsh - most bios are 40-60)
   - What SPECIFIC FLAWS and weaknesses lower this score?
   - Why would this bio FAIL in the competitive dating market?
   - What evidence PROVES this score is accurate?

5. CONFIDENCE EVALUATION:
   - How confident am I in this analysis (0-100)?
   - What factors support or limit my confidence?
   - What additional context would enhance my assessment?

6. STRATEGIC RECOMMENDATIONS:
   - What are my top 3-5 specific improvement recommendations?
   - Which changes would have the highest impact?
   - What is the implementation difficulty for each suggestion?

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "initial_assessment": {
    "immediate_impression": "Your first expert impression",
    "key_elements": ["element1", "element2", "element3"],
    "overall_communication": "What this bio communicates about the person"
  },
  "expert_evaluation": {
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"],
    "expert_specific_analysis": "Analysis from your specific expertise",
    "comparative_assessment": "How this compares to successful bios"
  },
  "specialized_analysis": {
    "personality_indicators": ["trait1", "trait2"],
    "communication_effectiveness": "Assessment of communication style",
    "market_positioning": "How this positions the person in the dating market",
    "target_audience_appeal": "Who this would appeal to"
  },
  "scoring_methodology": {
    "score": 45,
    "harsh_reality_check": "Why this bio would struggle in the real dating market",
    "critical_flaws": ["major_flaw1", "major_flaw2", "major_flaw3"],
    "evidence_for_low_score": ["specific_evidence1", "specific_evidence2"],
    "market_performance_prediction": "How this would actually perform (be realistic)",
    "score_components": {
      "writing_quality": 40,
      "personality_appeal": 50,
      "market_competitiveness": 45
    }
  },
  "confidence_evaluation": {
    "confidence": 88,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["limitation1", "limitation2"],
    "context_needed": "What additional context would help"
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement",
      "impact_level": "high|medium|low",
      "implementation_difficulty": "easy|moderate|challenging",
      "reasoning": "Why this recommendation is important",
      "priority": 1,
      "expected_outcome": "What improvement this would create"
    }
  ],
  "key_insights": ["insight1", "insight2", "insight3"]
}

Apply your specialized expertise while maintaining a constructive and helpful tone.`;
  }

  /**
   * Build chain-of-thought structure for bio analysis
   */
  private buildChainOfThoughtStructure(persona: ExpertPersona): string {
    return `
CHAIN-OF-THOUGHT REASONING STRUCTURE FOR BIO ANALYSIS:

Step 1: Expert First Impression
- "As a ${persona.type} expert, my immediate reaction to this bio is..."
- "The key elements that catch my attention are..."
- "From my professional perspective, this bio suggests..."

Step 2: Specialized Analysis Application
- "Applying my expertise in ${persona.expertise[0]}, I observe..."
- "The linguistic/psychological/market factors indicate..."
- "Based on my experience with ${persona.specializations[0]}, this demonstrates..."

Step 3: Comparative Market Assessment
- "Compared to successful bios I've analyzed..."
- "This bio would rank in the [percentile] because..."
- "The competitive positioning appears to be..."

Step 4: Brutal Honest Scoring
- "I'm scoring this [X]/100 because it has these CRITICAL FLAWS..."
- "This bio would FAIL in the dating market because..."
- "The harsh reality is that this bio..."
- "Compared to successful profiles, this lacks..."

Step 5: Critical Improvement Requirements
- "This bio MUST change these fundamental issues..."
- "Without these improvements, this profile will continue to fail..."
- "The brutal truth is that this person needs to..."
`;
  }

  /**
   * Build context information for bio analysis
   */
  private buildContextInfo(context: any): string {
    let contextStr = "ANALYSIS CONTEXT:\n";

    if (context.targetDemographic) {
      contextStr += `• Target Demographic: ${context.targetDemographic}\n`;
    }

    if (context.platform) {
      contextStr += `• Platform: ${context.platform}\n`;
    }

    if (context.relationshipGoals) {
      contextStr += `• Relationship Goals: ${context.relationshipGoals}\n`;
    }

    if (context.tone) {
      contextStr += `• Desired Tone: ${context.tone}\n`;
    }

    return contextStr + "\n";
  }

  /**
   * Get few-shot examples for the expert type
   */
  private getExamples(expertType: string): string[] {
    const examples: Record<string, string[]> = {
      psychology: [
        `Example Bio: "Love hiking, good food, and deep conversations. Looking for someone who can make me laugh and isn't afraid to be vulnerable."
        Analysis: "While this attempts emotional depth, it's actually a collection of dating clichés. 'Deep conversations' and 'vulnerability' are overused buzzwords that signal virtue signaling rather than genuine emotional intelligence. The bio lacks specificity and personality. Score: 52/100 because it's generic and forgettable despite good intentions."`,

        `Example Bio: "Just looking for fun, nothing serious. Hit me up if you're down for whatever."
        Analysis: "This bio screams emotional unavailability and poor communication skills. The casual dismissal of serious connections and vague 'whatever' language suggests someone who can't articulate their needs or commit to anything meaningful. Score: 18/100 because this actively repels quality matches and attracts only hookup culture."`,
      ],

      dating_coach: [
        `Example Bio: "Entrepreneur who loves weekend adventures and trying new restaurants. Seeking a partner in crime for life's next chapter. Dog lover and terrible cook - you've been warned!"
        Analysis: "This bio uses tired dating clichés like 'partner in crime' and 'life's next chapter' that appear on thousands of profiles. While the self-deprecating humor about cooking is decent, the overall message is generic and doesn't differentiate from the competition. Score: 61/100 - mediocre execution of overused formulas."`,

        `Example Bio: "I'm perfect, looking for my soulmate. Must be 6'+ and love fitness. No drama or games."
        Analysis: "This bio is a dating disaster. The narcissistic 'perfect' claim combined with shallow physical requirements and negative 'no drama' language creates an entitled, demanding tone that repels quality matches. Score: 12/100 because this actively sabotages dating success."`,
      ],

      data_science: [
        `Example Bio: "Software engineer who rock climbs on weekends. Love craft beer and board games. Looking for someone to explore the city with."
        Analysis: "While this hits common interest points, it's a stereotypical tech bro profile that blends into the crowd. The activities are predictable for the demographic and lack personality. Data shows this type performs average at best due to oversaturation. Score: 48/100 based on competitive analysis."`,

        `Example Bio: "Just ask me anything you want to know."
        Analysis: "This lazy bio shows 67% lower engagement rates and 78% fewer quality conversations. Zero conversation starters, no personality indicators, and puts all effort on the match. Algorithmic death sentence. Score: 8/100 based on platform performance data."`,
      ],

      fashion: [
        `Example Bio: "Vintage vinyl collector with a weakness for Sunday farmers markets. Equally comfortable in hiking boots or dress shoes. Seeking someone who appreciates both adventure and elegance."
        Analysis: "This tries too hard to appear sophisticated and comes across as pretentious. The 'hiking boots or dress shoes' line is forced and the 'adventure and elegance' phrase is clichéd. Reads like someone trying to impress rather than being authentic. Score: 54/100 because it feels manufactured rather than genuine."`,

        `Example Bio: "Love partying and getting wasted every weekend. Looking for someone who can keep up with my lifestyle."
        Analysis: "This bio screams poor life choices and potential substance abuse issues. The focus on 'getting wasted' suggests immaturity and lack of self-control. Completely unappealing to quality matches. Score: 9/100 because this actively destroys dating prospects."`,
      ],
    };

    return examples[expertType] || [];
  }
}
