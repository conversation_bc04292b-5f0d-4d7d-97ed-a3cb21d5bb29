import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface WizardData {
  // Step 1: Welcome
  hasSeenWelcome?: boolean;

  // Step 2: Goals
  primaryGoal?: "more_matches" | "better_conversations" | "serious_relationship" | "casual_dating";
  secondaryGoals?: string[];
  lookingFor?: string;

  // Step 3: First Name
  name?: string;

  // Step 4: Birth Year
  age?: number;

  // Step 5: Location
  location?: string;

  // Step 6: Occupation
  occupation?: string;

  // Step 7: Preferences
  targetAgeRange?: { min: number; max: number };
  targetGender?: "men" | "women" | "everyone";
  relationshipType?: "casual" | "serious" | "open";

  // Step 8: Photo Upload (no analysis during wizard)
  uploadedPhotos?: Array<{
    id: string;
    fileName: string;
    preview: string;
    file?: File;
  }>;
  photoUploadCompleted?: boolean;

  // Step 9: Bio Input
  originalBio?: string;

  // Step 10: Tone Selection
  selectedTone?: "witty" | "sincere" | "adventurous";
  toneSelectionCompleted?: boolean;

  // Step 11: Bio Review & Edit
  editedBio?: string;
  bioReviewCompleted?: boolean;

  // Step 12: Final Summary (scores shown at the end)
  finalScores?: {
    photoScore: number;
    bioScore: number;
    overallScore: number;
  };
  wizardCompleted?: boolean;

  // Legacy fields (for backward compatibility)
  improvedBio?: string;
  bioAnalysisResult?: any;
  bioAnalysisCompleted?: boolean;
  photoAnalysisCompleted?: boolean;
}

interface WizardStore {
  // State
  currentStep: number;
  wizardData: WizardData;
  completedSteps: Set<number>;

  // Actions
  setCurrentStep: (step: number) => void;
  updateWizardData: (data: Partial<WizardData>) => void;
  markStepCompleted: (step: number, stepData?: any) => void;
  isStepCompleted: (step: number) => boolean;
  resetWizard: () => void;

  // Navigation helpers
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  canGoToStep: (step: number) => boolean;
}

export const useWizardStore = create<WizardStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentStep: 1,
      wizardData: {},
      completedSteps: new Set<number>(),

      // Actions
      setCurrentStep: (step: number) => {
        console.log(`🧙‍♂️ Wizard: Moving to step ${step}`);
        set({ currentStep: step });
      },

      updateWizardData: (data: Partial<WizardData>) => {
        console.log("🧙‍♂️ Wizard: Updating data", data);
        set((state) => ({
          wizardData: { ...state.wizardData, ...data },
        }));
      },

      markStepCompleted: (step: number, stepData?: any) => {
        console.log(`🧙‍♂️ Wizard: Marking step ${step} as completed`, stepData);

        set((state) => {
          const newCompletedSteps = new Set(state.completedSteps);
          newCompletedSteps.add(step);

          let updatedWizardData = state.wizardData;
          if (stepData) {
            updatedWizardData = { ...state.wizardData, ...stepData };
          }

          return {
            completedSteps: newCompletedSteps,
            wizardData: updatedWizardData,
          };
        });
      },

      isStepCompleted: (step: number) => {
        return get().completedSteps.has(step);
      },

      resetWizard: () => {
        console.log("🧙‍♂️ Wizard: Resetting wizard state");
        set({
          currentStep: 1,
          wizardData: {},
          completedSteps: new Set<number>(),
        });
      },

      // Navigation helpers
      goToNextStep: () => {
        const { currentStep } = get();
        if (currentStep < 12) {
          set({ currentStep: currentStep + 1 });
        }
      },

      goToPreviousStep: () => {
        const { currentStep } = get();
        if (currentStep > 1) {
          set({ currentStep: currentStep - 1 });
        }
      },

      canGoToStep: (step: number) => {
        const { completedSteps } = get();
        // Can go to step if all previous steps are completed
        for (let i = 1; i < step; i++) {
          if (!completedSteps.has(i)) {
            return false;
          }
        }
        return true;
      },
    }),
    {
      name: "tinderop-wizard-storage",
      partialize: (state) => ({
        currentStep: state.currentStep,
        wizardData: state.wizardData,
        completedSteps: Array.from(state.completedSteps), // Convert Set to Array for persistence
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Convert Array back to Set after rehydration
          state.completedSteps = new Set(state.completedSteps as any);
          console.log("🧙‍♂️ Wizard: Rehydrated state", state);
        }
      },
    }
  )
);
