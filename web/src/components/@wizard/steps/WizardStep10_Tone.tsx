import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep10Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

const toneOptions = [
  {
    id: "sincere",
    name: "Sincere",
    description: "Authentic and genuine, focuses on real connections",
    emoji: "💝",
    example: "Genuine person looking for meaningful connections...",
    color: "from-blue-500 to-blue-600",
  },
  {
    id: "witty",
    name: "Witty",
    description: "Humorous and clever, shows your personality",
    emoji: "😄",
    example: "Professional overthinker seeking adventure partner...",
    color: "from-yellow-500 to-orange-500",
  },
  {
    id: "adventurous",
    name: "Adventurous",
    description: "Bold and exciting, highlights your active lifestyle",
    emoji: "🌟",
    example: "Life's too short for boring weekends...",
    color: "from-green-500 to-emerald-600",
  },
];

export function WizardStep10_Tone({ onComplete, wizardData, isCompleted }: WizardStep10Props) {
  const [selectedTone, setSelectedTone] = useState<"sincere" | "witty" | "adventurous" | "">(
    wizardData.selectedTone || ""
  );

  const isFormValid = selectedTone !== "";

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        selectedTone: selectedTone as "sincere" | "witty" | "adventurous",
        toneSelectionCompleted: true,
      });
    }
  }, [selectedTone, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  const handleToneSelect = (toneId: "sincere" | "witty" | "adventurous") => {
    setSelectedTone(toneId);
  };

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          Choose Your Bio Tone
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          Select the personality style that best represents you
        </motion.p>
      </div>

      {/* Tone Options */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        {toneOptions.map((tone, index) => (
          <motion.div
            key={tone.id}
            initial={{ x: -40, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 + index * 0.1 }}
          >
            <Card
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                selectedTone === tone.id
                  ? "ring-2 ring-flame-red border-flame-red/50 bg-flame-red/5"
                  : "border-graphite-20 hover:border-flame-red/30"
              )}
              onClick={() => handleToneSelect(tone.id as "sincere" | "witty" | "adventurous")}
            >
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Tone Icon */}
                  <div
                    className={cn(
                      "flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-white text-xl bg-gradient-to-br",
                      tone.color
                    )}
                  >
                    {tone.emoji}
                  </div>

                  {/* Tone Details */}
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-graphite-90">{tone.name}</h3>
                      {selectedTone === tone.id && (
                        <div className="w-6 h-6 bg-flame-red rounded-full flex items-center justify-center">
                          <Heart className="w-4 h-4 text-white fill-current" />
                        </div>
                      )}
                    </div>

                    <p className="text-sm text-graphite-70">{tone.description}</p>

                    <div className="bg-graphite-5 rounded-lg p-3 border border-graphite-10">
                      <p className="text-xs text-graphite-60 italic">"{tone.example}"</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Tone Selection Tips */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <Card className="border border-blue-200 bg-blue-50">
          <CardContent className="p-6 space-y-3">
            <h4 className="font-semibold text-graphite-90 flex items-center gap-2">
              <Palette className="w-4 h-4 text-blue-600" />
              Tone Selection Tips
            </h4>
            <ul className="space-y-2 text-sm text-graphite-70">
              <li className="flex items-start gap-2">
                <Sparkles className="w-3 h-3 text-blue-600 mt-1 flex-shrink-0" />
                <span>Choose the tone that feels most authentic to your personality</span>
              </li>
              <li className="flex items-start gap-2">
                <Smile className="w-3 h-3 text-blue-600 mt-1 flex-shrink-0" />
                <span>Consider what type of conversations you want to have</span>
              </li>
              <li className="flex items-start gap-2">
                <Heart className="w-3 h-3 text-blue-600 mt-1 flex-shrink-0" />
                <span>Your tone will guide how we optimize your bio content</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {selectedTone && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            ✓ {toneOptions.find((t) => t.id === selectedTone)?.name} tone selected
          </div>
        </motion.div>
      )}
    </div>
  );
}
