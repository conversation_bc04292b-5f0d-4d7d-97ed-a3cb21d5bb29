import { motion } from "framer-motion";
import { Camera, CheckCircle, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { createImagePreview, revokeImagePreview } from "@/lib/storage";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep8Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

interface PhotoSlot {
  id: string;
  fileName?: string;
  preview?: string;
  file?: File;
  isEmpty: boolean;
}

export function WizardStep8_Photos({ onComplete, wizardData, isCompleted }: WizardStep8Props) {
  // Initialize 9 empty slots (3x3 grid)
  const [photoSlots, setPhotoSlots] = useState<PhotoSlot[]>(() =>
    Array.from({ length: 9 }, (_, index) => ({
      id: `slot-${index}`,
      isEmpty: true,
    }))
  );

  const hasPhotos = photoSlots.some((slot) => !slot.isEmpty);
  const filledSlots = photoSlots.filter((slot) => !slot.isEmpty);
  const isFormValid = filledSlots.length >= 3; // Require at least 3 photos

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      const photoData = filledSlots
        .filter((slot) => slot.fileName && slot.preview)
        .map((slot) => ({
          id: slot.id,
          fileName: slot.fileName as string,
          preview: slot.preview as string,
          file: slot.file,
        }));

      onComplete({
        uploadedPhotos: photoData,
        photoUploadCompleted: true,
      });
    }
  }, [filledSlots, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  const handleSlotDrop = useCallback(async (acceptedFiles: File[], slotIndex: number) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0]; // Only take the first file
    const preview = await createImagePreview(file);

    setPhotoSlots((prev) => {
      const newSlots = [...prev];
      // Clean up previous image in this slot if any
      const existingPreview = newSlots[slotIndex].preview;
      if (!newSlots[slotIndex].isEmpty && existingPreview) {
        revokeImagePreview(existingPreview);
      }

      newSlots[slotIndex] = {
        id: `slot-${slotIndex}-${Date.now()}`,
        fileName: file.name,
        preview,
        file,
        isEmpty: false,
      };
      return newSlots;
    });
  }, []);

  const removePhoto = (slotIndex: number) => {
    setPhotoSlots((prev) => {
      const newSlots = [...prev];
      const slot = newSlots[slotIndex];

      // Clean up preview URL
      if (slot.preview) {
        revokeImagePreview(slot.preview);
      }

      // Reset slot to empty
      newSlots[slotIndex] = {
        id: `slot-${slotIndex}`,
        isEmpty: true,
      };

      return newSlots;
    });
  };

  // Create individual dropzones for each slot (must be at top level)
  const slotDropzones = photoSlots.map((_, index) =>
    useDropzone({
      onDrop: (files) => handleSlotDrop(files, index),
      accept: { "image/*": [] },
      maxFiles: 1,
      noClick: !photoSlots[index]?.isEmpty,
    })
  );

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          Upload Your Photos
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          Upload 3-9 photos. Drag photos to different slots to reorder them.
        </motion.p>
      </div>

      {/* Photo Grid */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-graphite-90">
            Your Photos ({filledSlots.length}/9)
          </h3>
          {filledSlots.length >= 3 && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Upload Complete
            </Badge>
          )}
        </div>

        {/* 3x3 Photo Grid */}
        <div className="grid grid-cols-3 gap-3 sm:gap-4 max-w-2xl mx-auto">
          {photoSlots.map((slot, index) => {
            const { getRootProps, getInputProps, isDragActive } = slotDropzones[index];
            const isMainPhoto = index === 0;

            return (
              <motion.div
                key={slot.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="relative aspect-[7/10]" // Tinder's image aspect ratio
              >
                {slot.isEmpty ? (
                  // Empty slot - drop zone
                  <div
                    {...getRootProps()}
                    className={cn(
                      "w-full h-full border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors",
                      isDragActive
                        ? "border-flame-red bg-flame-red/10"
                        : "border-graphite-30 hover:border-flame-red/50 hover:bg-flame-red/5",
                      "touch-target"
                    )}
                  >
                    <input {...getInputProps()} />
                    {isMainPhoto && (
                      <p className="text-xs font-medium text-flame-red mb-2 text-center px-1">
                        Main Photo
                      </p>
                    )}
                    <Camera className="w-6 h-6 sm:w-8 sm:h-8 text-graphite-40 mb-2" />
                    <p className="text-xs text-graphite-60 text-center px-1">
                      {isDragActive ? "Drop here" : "Tap to add"}
                    </p>
                    {isMainPhoto && (
                      <p className="text-xs text-graphite-50 text-center px-1 mt-1">
                        This will be your primary photo
                      </p>
                    )}
                  </div>
                ) : (
                  // Filled slot - photo display
                  <Card className="w-full h-full overflow-hidden">
                    <div className="relative w-full h-full">
                      <img
                        src={slot.preview}
                        alt={slot.fileName}
                        className="w-full h-full object-cover"
                      />

                      {/* Main Photo Badge */}
                      {isMainPhoto && (
                        <div className="absolute top-2 left-2">
                          <Badge className="bg-flame-red text-white text-xs">Main</Badge>
                        </div>
                      )}

                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors touch-target"
                      >
                        <X className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </Card>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* Instructions */}
        <div className="text-center space-y-2">
          <p className="text-sm text-graphite-60">
            JPG, PNG •{" "}
            {filledSlots.length < 3
              ? `Need ${3 - filledSlots.length} more photos`
              : `${9 - filledSlots.length} slots remaining`}
          </p>
          {hasPhotos && (
            <p className="text-xs text-graphite-50">Drag photos between slots to reorder them</p>
          )}
        </div>
      </motion.div>

      {/* Validation Status */}
      {filledSlots.length < 3 && hasPhotos && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-lg text-sm">
            Need at least 3 photos to continue
          </div>
        </motion.div>
      )}

      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            Photos uploaded successfully! Ready for the next step.
          </div>
        </motion.div>
      )}
    </div>
  );
}
