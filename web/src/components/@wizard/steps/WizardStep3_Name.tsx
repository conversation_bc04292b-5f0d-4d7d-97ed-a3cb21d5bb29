import { motion } from "framer-motion";
import { CheckCircle, User } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep3Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep3_Name({ onComplete, wizardData, isCompleted }: WizardStep3Props) {
  const [firstName, setFirstName] = useState(wizardData.name || "");

  const isFormValid = firstName.trim().length >= 2;

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        name: firstName.trim(),
      });
    }
  }, [firstName, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          What's Your First Name?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          Let's start with how you'd like to be called
        </motion.p>
      </div>

      {/* Name Input Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="max-w-md mx-auto"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <Label
                htmlFor="first-name"
                className="flex items-center gap-2 text-graphite-90 text-lg font-medium"
              >
                <User className="w-5 h-5" />
                First Name
              </Label>

              <Input
                id="first-name"
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="Enter your first name"
                className="text-lg h-12 text-center"
                maxLength={50}
                autoComplete="given-name"
                autoFocus
              />

              <div className="text-center text-sm text-graphite-60">
                {firstName.length < 2 && firstName.length > 0
                  ? "Please enter at least 2 characters"
                  : firstName.length >= 2
                    ? "✓ Looks good!"
                    : "This will help personalize your experience"}
              </div>
            </motion.div>

            {/* Tips */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="bg-blue-50 rounded-lg p-4 space-y-2"
            >
              <h4 className="font-semibold text-graphite-90 text-sm">💡 Tip</h4>
              <p className="text-sm text-graphite-70">
                Use the name you'd want potential matches to call you. This helps create a more
                personal connection.
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {firstName.length > 0 && !isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-lg text-sm">
            Name must be at least 2 characters
          </div>
        </motion.div>
      )}

      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            Nice to meet you, {firstName}!
          </div>
        </motion.div>
      )}
    </div>
  );
}
