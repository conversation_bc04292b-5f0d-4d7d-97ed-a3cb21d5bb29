import { motion } from "framer-motion";
import { <PERSON>, <PERSON>rk<PERSON>, Star, Target, TrendingUp, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep1Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
  onNext?: () => void;
}

export function WizardStep1_Info({ onComplete, wizardData, isCompleted, onNext }: WizardStep1Props) {
  const [hasSeenWelcome, setHasSeenWelcome] = useState(wizardData.hasSeenWelcome || false);

  useEffect(() => {
    if (hasSeenWelcome && !isCompleted) {
      onComplete({ hasSeenWelcome: true });
    }
  }, [hasSeenWelcome, isCompleted, onComplete]);

  const features = [
    {
      icon: Target,
      title: "AI-Powered Analysis",
      description: "Get detailed feedback on your photos and bio from advanced AI models",
    },
    {
      icon: TrendingUp,
      title: "Proven Results",
      description: "Our optimization techniques have helped thousands get more matches",
    },
    {
      icon: Users,
      title: "Personalized Advice",
      description: "Tailored recommendations based on your goals and dating platform",
    },
  ];

  const handleGetStarted = () => {
    if (hasSeenWelcome && onNext) {
      // If already seen welcome, act like Next button
      onNext();
    } else {
      // First time, just mark as seen
      setHasSeenWelcome(true);
    }
  };

  return (
    <div className="space-y-mobile">
      {/* Hero Section */}
      <div className="text-center space-y-4 sm:space-y-6">
        <motion.div
          initial={{ scale: 0.8, opacity: 0, rotate: -180 }}
          animate={{ scale: 1, opacity: 1, rotate: 0 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          className="flex justify-center"
        >
          <motion.div
            className="relative cursor-pointer"
            whileHover={{ rotate: 360, scale: 1.1 }}
            transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
          >
            <Heart className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-flame-red fill-current" />
            <Sparkles className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-sparks-pink absolute -top-1 -right-1 lg:-top-2 lg:-right-2" />
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-3 sm:space-y-4 lg:space-y-6"
        >
          <h1 className="text-responsive-h1 font-bold text-graphite-90">Welcome to TinderOP</h1>
          <p className="text-responsive-body-lg text-graphite-60 max-w-2xl lg:max-w-3xl mx-auto mb-6">
            Transform your dating profile from overlooked to overbooked. Our AI-powered platform
            will help you optimize every aspect of your profile to get more matches and better
            conversations.
          </p>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex justify-center"
          >
            <Button onClick={handleGetStarted} size="lg" className="flex items-center gap-2 h-11">
              <span>{hasSeenWelcome ? "Ready to Continue" : "Let's Get Started"}</span>
              <Sparkles className="ml-2 h-4 w-4" />
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Features Grid */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-5 sm:gap-6 lg:gap-8"
      >
        {features.map((feature) => (
          <Card
            key={feature.title}
            className="border border-graphite-20 hover:border-flame-red/30 transition-colors hover:shadow-md"
          >
            <CardContent className="p-5 sm:p-6 lg:p-8 text-center space-y-4 lg:space-y-5">
              <div className="flex justify-center">
                <div className="p-3 lg:p-4 rounded-full bg-flame-red/10">
                  <feature.icon className="w-6 h-6 lg:w-7 lg:h-7 text-flame-red" />
                </div>
              </div>
              <h3 className="font-semibold text-graphite-90 text-base lg:text-lg leading-tight">
                {feature.title}
              </h3>
              <p className="text-sm lg:text-base text-graphite-60 leading-relaxed">
                {feature.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </motion.div>

      {/* What to Expect */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="bg-gradient-to-r from-flame-red/5 to-sparks-pink/5 rounded-lg p-5 sm:p-6 lg:p-8"
      >
        <h3 className="font-semibold text-graphite-90 mb-4 lg:mb-6 flex items-center gap-2">
          <Star className="w-5 h-5 lg:w-6 lg:h-6 text-flame-red" />
          What to Expect
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6 text-sm lg:text-base text-graphite-70">
          <div className="space-y-3">
            <p>✓ Set your dating goals and preferences</p>
            <p>✓ Upload and analyze your photos</p>
            <p>✓ Optimize your bio for maximum appeal</p>
          </div>
          <div className="space-y-3">
            <p>✓ Get personalized improvement tips</p>
            <p>✓ Learn what works on your platform</p>
            <p>✓ Start getting better matches</p>
          </div>
        </div>
      </motion.div>

      {/* Next Step Instructions */}
      {hasSeenWelcome && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="text-center"
        >
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-sm lg:text-base text-graphite-60"
          >
            Click "Next" to continue to goal setting
          </motion.p>
        </motion.div>
      )}
    </div>
  );
}
