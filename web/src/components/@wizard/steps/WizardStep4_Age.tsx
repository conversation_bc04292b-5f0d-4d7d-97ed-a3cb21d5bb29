import { motion } from "framer-motion";
import { Calendar, CheckCircle } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { YearPicker } from "@/components/ui/year-picker";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep4Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep4_Age({ onComplete, wizardData, isCompleted }: WizardStep4Props) {
  const [birthYear, setBirthYear] = useState<number | undefined>(
    wizardData.age ? new Date().getFullYear() - wizardData.age : undefined
  );

  const currentYear = new Date().getFullYear();
  const age = birthYear ? currentYear - birthYear : 0;
  const isValidAge = age >= 18 && age <= 100;
  const isFormValid = birthYear !== undefined && isValidAge;

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        age,
      });
    }
  }, [age, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          What Year Were You Born?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          This helps us provide age-appropriate recommendations
        </motion.p>
      </div>

      {/* Birth Year Input Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="max-w-md mx-auto"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <Label
                htmlFor="birth-year"
                className="flex items-center gap-2 text-graphite-90 text-lg font-medium"
              >
                <Calendar className="w-5 h-5" />
                Birth Year
              </Label>

              <YearPicker
                value={birthYear}
                onChange={setBirthYear}
                placeholder="Select your birth year"
                className="w-full text-lg h-12"
              />

              <div className="text-center text-sm text-graphite-60">
                {birthYear && age < 18
                  ? "Must be 18 or older to use TinderOP"
                  : birthYear && age > 100
                    ? "Please enter a valid birth year"
                    : birthYear && isValidAge
                      ? `✓ Age: ${age} years old`
                      : "Select the year you were born"}
              </div>
            </motion.div>

            {/* Age Display */}
            {birthYear && isValidAge && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, type: "spring" }}
                className="text-center p-4 bg-flame-red/10 rounded-lg"
              >
                <div className="text-2xl font-bold text-flame-red">{age}</div>
                <div className="text-sm text-graphite-70">years old</div>
              </motion.div>
            )}

            {/* Privacy Notice */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="bg-blue-50 rounded-lg p-4 space-y-2"
            >
              <h4 className="font-semibold text-graphite-90 text-sm">🔒 Privacy</h4>
              <p className="text-sm text-graphite-70">
                Your age helps us provide relevant advice. We never share your personal information.
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {birthYear && !isValidAge && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-lg text-sm">
            {age < 18 ? "Must be 18 or older" : "Please enter a valid birth year"}
          </div>
        </motion.div>
      )}

      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            Perfect! You're {age} years old.
          </div>
        </motion.div>
      )}
    </div>
  );
}
