import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, MapP<PERSON>, SkipForward } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep5Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep5_Location({ onComplete, wizardData, isCompleted }: WizardStep5Props) {
  const [location, setLocation] = useState(wizardData.location || "");
  const [hasSkipped, setHasSkipped] = useState(false);

  const isFormValid = location.trim().length > 0 || hasSkipped;

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        location: location.trim(),
      });
    }
  }, [location, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  const handleSkip = () => {
    setHasSkipped(true);
    setLocation("");
  };

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          Where Are You Located?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          This helps us understand your local dating market (optional)
        </motion.p>
      </div>

      {/* Location Input Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="max-w-md mx-auto"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <Label
                htmlFor="location"
                className="flex items-center gap-2 text-graphite-90 text-lg font-medium"
              >
                <MapPin className="w-5 h-5" />
                Location (Optional)
              </Label>

              <Input
                id="location"
                type="text"
                value={location}
                onChange={(e) => {
                  setLocation(e.target.value);
                  setHasSkipped(false);
                }}
                placeholder="e.g., New York, NY or London, UK"
                className="text-lg h-12 text-center"
                maxLength={100}
                autoComplete="address-level2"
                autoFocus
              />

              <div className="text-center text-sm text-graphite-60">
                {location.length > 0
                  ? "✓ Great! This helps with local advice"
                  : hasSkipped
                    ? "Skipped - that's okay too!"
                    : "Enter your city or skip if you prefer"}
              </div>
            </motion.div>

            {/* Skip Option */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-center"
            >
              <Button
                variant="ghost"
                onClick={handleSkip}
                className="text-graphite-60 hover:text-graphite-90"
                disabled={hasSkipped}
              >
                <SkipForward className="w-4 h-4 mr-2" />
                Skip this step
              </Button>
            </motion.div>

            {/* Benefits */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="bg-blue-50 rounded-lg p-4 space-y-2"
            >
              <h4 className="font-semibold text-graphite-90 text-sm">💡 Why location helps</h4>
              <ul className="text-sm text-graphite-70 space-y-1">
                <li>• Better photo recommendations for your area</li>
                <li>• Location-relevant bio suggestions</li>
                <li>• Understanding local dating culture</li>
              </ul>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            {location.length > 0 ? `Location set: ${location}` : "Skipped location - that's fine!"}
          </div>
        </motion.div>
      )}
    </div>
  );
}
