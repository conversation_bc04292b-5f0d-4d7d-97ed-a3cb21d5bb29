import { motion } from "framer-motion";
import { FileText, User } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep9Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep9_Bio({ onComplete, wizardData, isCompleted }: WizardStep9Props) {
  const [originalBio, setOriginalBio] = useState(wizardData.originalBio || "");

  const hasValidBio = originalBio.trim().length >= 20;
  const isFormValid = hasValidBio;

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        originalBio: originalBio.trim(),
      });
    }
  }, [originalBio, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          Enter Your Current Bio
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          Share your current Tinder bio so we can help you improve it
        </motion.p>
      </div>

      {/* Bio Input Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-3"
            >
              <Label
                htmlFor="bio"
                className="flex items-center gap-2 text-graphite-90 text-lg font-medium"
              >
                <FileText className="w-5 h-5" />
                Your Current Bio
              </Label>

              <Textarea
                id="bio"
                value={originalBio}
                onChange={(e) => setOriginalBio(e.target.value)}
                placeholder="I love hiking, cooking, and discovering new coffee shops. Looking for someone who can make me laugh and isn't afraid of adventure..."
                className="min-h-32 text-base leading-relaxed resize-none"
                maxLength={500}
              />

              <div className="flex justify-between items-center text-sm">
                <p className="text-graphite-60">
                  {originalBio.length < 20
                    ? `Need at least ${20 - originalBio.length} more characters`
                    : "✓ Good length for analysis"}
                </p>
                <p className="text-graphite-50">{originalBio.length}/500</p>
              </div>
            </motion.div>

            {/* Bio Tips */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="bg-blue-50 rounded-lg p-4 space-y-3"
            >
              <h4 className="font-semibold text-graphite-90 flex items-center gap-2">
                <User className="w-4 h-4 text-blue-600" />
                Bio Tips
              </h4>
              <ul className="space-y-2 text-sm text-graphite-70">
                <li>• Include your interests and hobbies</li>
                <li>• Show your personality and sense of humor</li>
                <li>• Mention what you're looking for</li>
                <li>• Keep it authentic and genuine</li>
              </ul>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {originalBio.length > 0 && !hasValidBio && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-lg text-sm">
            Bio needs to be at least 20 characters for analysis
          </div>
        </motion.div>
      )}

      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            ✓ Bio ready for optimization
          </div>
        </motion.div>
      )}
    </div>
  );
}
