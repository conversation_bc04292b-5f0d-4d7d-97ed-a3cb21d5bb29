import { motion } from "framer-motion";
import { Briefcase, CheckCircle, SkipForward } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep6Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep6_Occupation({ onComplete, wizardData, isCompleted }: WizardStep6Props) {
  const [occupation, setOccupation] = useState(wizardData.occupation || "");
  const [hasSkipped, setHasSkipped] = useState(false);

  const isFormValid = occupation.trim().length > 0 || hasSkipped;

  const handleComplete = useCallback(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        occupation: occupation.trim(),
      });
    }
  }, [occupation, isFormValid, isCompleted, onComplete]);

  useEffect(() => {
    handleComplete();
  }, [handleComplete]);

  const handleSkip = () => {
    setHasSkipped(true);
    setOccupation("");
  };

  const popularOccupations = [
    "Software Engineer",
    "Teacher",
    "Student",
    "Designer",
    "Marketing",
    "Sales",
    "Healthcare",
    "Finance",
    "Entrepreneur",
    "Creative",
  ];

  return (
    <div className="space-y-mobile">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-responsive-h2 font-bold text-graphite-90"
        >
          What Do You Do for Work?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-responsive-body text-graphite-60"
        >
          This helps us tailor bio suggestions to your profession (optional)
        </motion.p>
      </div>

      {/* Occupation Input Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="max-w-md mx-auto"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <Label
                htmlFor="occupation"
                className="flex items-center gap-2 text-graphite-90 text-lg font-medium"
              >
                <Briefcase className="w-5 h-5" />
                Occupation (Optional)
              </Label>

              <Input
                id="occupation"
                type="text"
                value={occupation}
                onChange={(e) => {
                  setOccupation(e.target.value);
                  setHasSkipped(false);
                }}
                placeholder="e.g., Software Engineer, Teacher, Student"
                className="text-lg h-12 text-center"
                maxLength={100}
                autoComplete="organization-title"
                autoFocus
              />

              <div className="text-center text-sm text-graphite-60">
                {occupation.length > 0
                  ? "✓ Perfect! This helps with bio suggestions"
                  : hasSkipped
                    ? "Skipped - no problem!"
                    : "Enter your job title or field"}
              </div>
            </motion.div>

            {/* Quick Select Options */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="space-y-3"
            >
              <h4 className="text-sm font-medium text-graphite-90">Quick select:</h4>
              <div className="flex flex-wrap gap-2">
                {popularOccupations.map((job) => (
                  <Button
                    key={job}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setOccupation(job);
                      setHasSkipped(false);
                    }}
                    className="text-xs h-8 hover:border-flame-red/50"
                  >
                    {job}
                  </Button>
                ))}
              </div>
            </motion.div>

            {/* Skip Option */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-center"
            >
              <Button
                variant="ghost"
                onClick={handleSkip}
                className="text-graphite-60 hover:text-graphite-90"
                disabled={hasSkipped}
              >
                <SkipForward className="w-4 h-4 mr-2" />
                Skip this step
              </Button>
            </motion.div>

            {/* Benefits */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="bg-blue-50 rounded-lg p-4 space-y-2"
            >
              <h4 className="font-semibold text-graphite-90 text-sm">💼 Why occupation helps</h4>
              <ul className="text-sm text-graphite-70 space-y-1">
                <li>• Professional bio suggestions</li>
                <li>• Industry-specific conversation starters</li>
                <li>• Better match with compatible careers</li>
              </ul>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Validation Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            {occupation.length > 0
              ? `Occupation: ${occupation}`
              : "Skipped occupation - no worries!"}
          </div>
        </motion.div>
      )}
    </div>
  );
}
