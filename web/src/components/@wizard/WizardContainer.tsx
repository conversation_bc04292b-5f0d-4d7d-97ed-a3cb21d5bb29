import { useNavigate } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { useWizardStore } from "@/stores/wizardStore";

// Import wizard steps
import { WizardStep1_Info } from "./steps/WizardStep1_Info";
import { WizardStep2_Goals } from "./steps/WizardStep2_Goals";
import { WizardStep3_Name } from "./steps/WizardStep3_Name";
import { WizardStep4_Age } from "./steps/WizardStep4_Age";
import { WizardStep5_Location } from "./steps/WizardStep5_Location";
import { WizardStep6_Occupation } from "./steps/WizardStep6_Occupation";
import { WizardStep7_Preferences } from "./steps/WizardStep7_Preferences";
import { WizardStep8_Photos } from "./steps/WizardStep8_Photos";
import { WizardStep9_Bio } from "./steps/WizardStep9_Bio";
import { WizardStep10_Tone } from "./steps/WizardStep10_Tone";

const WIZARD_STEPS = [
  { id: 1, title: "Info", component: WizardStep1_Info },
  { id: 2, title: "Goals", component: WizardStep2_Goals },
  { id: 3, title: "Name", component: WizardStep3_Name },
  { id: 4, title: "Age", component: WizardStep4_Age },
  { id: 5, title: "Location", component: WizardStep5_Location },
  { id: 6, title: "Occupation", component: WizardStep6_Occupation },
  { id: 7, title: "Preferences", component: WizardStep7_Preferences },
  { id: 8, title: "Photos", component: WizardStep8_Photos },
  { id: 9, title: "Bio", component: WizardStep9_Bio },
  { id: 10, title: "Tone", component: WizardStep10_Tone },
] as const;

interface WizardContainerProps {
  onComplete?: () => void;
}

export function WizardContainer({ onComplete }: WizardContainerProps) {
  const navigate = useNavigate();
  const {
    currentStep,
    setCurrentStep,
    wizardData,
    isStepCompleted,
    markStepCompleted,
    resetWizard,
  } = useWizardStore();

  const [isTransitioning, setIsTransitioning] = useState(false);

  // Ensure currentStep is within bounds
  const validCurrentStep = Math.max(1, Math.min(currentStep, WIZARD_STEPS.length));
  const currentStepData = WIZARD_STEPS[validCurrentStep - 1];

  if (!currentStepData) {
    console.error(`❌ Wizard: Invalid step ${currentStep}, resetting to step 1`);
    setCurrentStep(1);
    return <div>Loading...</div>;
  }

  const CurrentStepComponent = currentStepData.component;
  const progress = (validCurrentStep / WIZARD_STEPS.length) * 100;

  const canGoNext = isStepCompleted(validCurrentStep);
  const canGoPrevious = validCurrentStep > 1;
  const isLastStep = validCurrentStep === WIZARD_STEPS.length;

  const handleNext = async () => {
    if (!canGoNext) return;

    setIsTransitioning(true);

    if (isLastStep) {
      // Complete wizard
      console.log("🎉 Wizard completed with data:", wizardData);
      onComplete?.();
      navigate({ to: "/dashboard" });
    } else {
      // Go to next step
      setTimeout(() => {
        setCurrentStep(validCurrentStep + 1);
        setIsTransitioning(false);
      }, 150);
    }
  };

  const handlePrevious = () => {
    if (!canGoPrevious) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentStep(validCurrentStep - 1);
      setIsTransitioning(false);
    }, 150);
  };

  const handleStepComplete = (stepData?: any) => {
    console.log(`✅ Step ${validCurrentStep} completed with data:`, stepData);
    markStepCompleted(validCurrentStep, stepData);
  };

  // Reset wizard on mount if needed
  useEffect(() => {
    // Only reset if we're starting fresh
    if (currentStep === 1 && Object.keys(wizardData).length === 0) {
      console.log("🔄 Initializing fresh wizard session");
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-md sm:max-w-3xl lg:max-w-5xl xl:max-w-6xl">
        {/* Progress Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3 sm:gap-2">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-graphite-90">
              Setup Your Profile
            </h1>
            <div className="text-sm sm:text-base text-graphite-60 font-medium">
              Step {validCurrentStep} of {WIZARD_STEPS.length}
            </div>
          </div>

          <Progress value={progress} className="h-2 sm:h-2.5" />

          <div className="flex justify-between mt-3 text-sm text-graphite-60">
            <span className="hidden sm:inline">Getting Started</span>
            <span className="sm:hidden">Start</span>
            <span className="hidden sm:inline">Profile Ready</span>
            <span className="sm:hidden">Ready</span>
          </div>
        </div>

        {/* Step Content */}
        <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
          <CardContent className="p-4 sm:p-6 lg:p-8 xl:p-10">
            {/* Step Component */}
            <div
              className={cn(
                "transition-all duration-300",
                isTransitioning && "opacity-50 scale-95"
              )}
            >
              <CurrentStepComponent
                onComplete={handleStepComplete}
                wizardData={wizardData}
                isCompleted={isStepCompleted(validCurrentStep)}
                onNext={handleNext}
              />
            </div>

            {/* Navigation */}
            <div className="mt-8 sm:mt-10 pt-6 sm:pt-8 border-t border-graphite-20">
              {/* Mobile Navigation - Stacked Layout */}
              <div className="flex flex-col gap-4 sm:hidden">
                {/* Progress Indicators */}
                <div className="flex items-center justify-center gap-2">
                  {WIZARD_STEPS.map((step, index) => (
                    <div
                      key={step.id}
                      className={cn(
                        "w-2 h-2 rounded-full transition-colors",
                        index + 1 < validCurrentStep
                          ? "bg-flame-red"
                          : index + 1 === validCurrentStep
                            ? "bg-flame-red/60"
                            : "bg-graphite-20"
                      )}
                    />
                  ))}
                </div>

                {/* Buttons Row */}
                <div className="flex gap-3">
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                    disabled={!canGoPrevious || isTransitioning}
                    className="flex items-center justify-center gap-2 flex-1 h-12 touch-target-lg"
                    size="lg"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    <span>Back</span>
                  </Button>

                  <Button
                    onClick={handleNext}
                    disabled={!canGoNext || isTransitioning}
                    className="flex items-center justify-center gap-2 flex-1 h-12 touch-target-lg"
                    size="lg"
                  >
                    <span>{isLastStep ? "Complete" : "Next"}</span>
                    {!isLastStep && <ArrowRight className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Desktop Navigation - Horizontal Layout */}
              <div className="hidden sm:flex justify-between items-center gap-6">
                <Button
                  variant="ghost"
                  onClick={handlePrevious}
                  disabled={!canGoPrevious || isTransitioning}
                  className="flex items-center gap-2 h-11"
                  size="lg"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Previous</span>
                </Button>

                <div className="flex items-center gap-3">
                  {WIZARD_STEPS.map((step, index) => (
                    <div
                      key={step.id}
                      className={cn(
                        "w-2.5 h-2.5 rounded-full transition-colors",
                        index + 1 < validCurrentStep
                          ? "bg-flame-red"
                          : index + 1 === validCurrentStep
                            ? "bg-flame-red/60"
                            : "bg-graphite-20"
                      )}
                    />
                  ))}
                </div>

                <Button
                  onClick={handleNext}
                  disabled={!canGoNext || isTransitioning}
                  className="flex items-center gap-2 h-11"
                  size="lg"
                >
                  <span>{isLastStep ? "Complete Setup" : "Next"}</span>
                  {!isLastStep && <ArrowRight className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
