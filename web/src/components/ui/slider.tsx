import * as SliderPrimitive from "@radix-ui/react-slider";
import * as React from "react";

import { cn } from "@/lib/utils";

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => {
  // Check if value is an array to determine if we need multiple thumbs
  const isRangeSlider = Array.isArray(props.value) || Array.isArray(props.defaultValue);
  const thumbCount = isRangeSlider ? 
    (props.value?.length || props.defaultValue?.length || 2) : 1;

  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn("relative flex w-full touch-none select-none items-center", className)}
      {...props}
    >
      <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-graphite-60/20">
        <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-flame-red to-sparks-pink" />
      </SliderPrimitive.Track>
      {Array.from({ length: thumbCount }).map((_, index) => (
        <SliderPrimitive.Thumb
          key={index}
          className="block h-5 w-5 rounded-full border-2 border-flame-red bg-flame-red ring-offset-cloud-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-flame-red focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-md"
        />
      ))}
    </SliderPrimitive.Root>
  );
});
Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider };
