import type { LucideIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  lastScore?: number;
  buttonText: string;
  buttonVariant?: "default" | "secondary" | "outline";
  isPro?: boolean;
  className?: string;
  onClick?: () => void;
  href?: string;
}

export function ActionCard({
  title,
  description,
  icon: Icon,
  lastScore,
  buttonText,
  buttonVariant = "default",
  isPro = false,
  className,
  onClick,
  href,
}: ActionCardProps) {
  const cardContent = (
    <Card
      className={cn(
        "h-full transition-all duration-200 hover:shadow-md cursor-pointer border border-slate-200",
        isPro && "border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50",
        className
      )}
    >
      <CardHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
        <CardTitle className="flex items-center gap-2 sm:gap-3 text-sm sm:text-base min-w-0">
          <div
            className={cn(
              "p-1.5 sm:p-2 rounded-lg flex-shrink-0",
              isPro ? "bg-amber-100 text-amber-600" : "bg-flame-red/10 text-flame-red"
            )}
          >
            <Icon className="h-4 w-4 sm:h-5 sm:w-5" />
          </div>
          <span className="text-slate-900 font-semibold truncate">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-3 sm:space-y-4 px-4 sm:px-6 pb-4 sm:pb-6">
        <p className="text-xs sm:text-sm text-slate-600 leading-relaxed">{description}</p>

        {lastScore !== undefined && (
          <div className="flex items-center justify-between text-xs sm:text-sm">
            <span className="text-slate-500">Last Score:</span>
            <span className="font-semibold text-slate-900">{lastScore}%</span>
          </div>
        )}

        <Button
          variant={buttonVariant}
          size="sm"
          className={cn(
            "w-full text-xs sm:text-sm h-8 sm:h-10",
            buttonVariant === "default" && "bg-slate-900 hover:bg-slate-800 text-white",
            isPro &&
              buttonVariant === "secondary" &&
              "bg-white border border-slate-300 text-slate-700 hover:bg-slate-50"
          )}
          onClick={onClick}
        >
          {isPro && buttonText === "Unlock Pro" && (
            <svg
              className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          )}
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );

  return cardContent;
}
