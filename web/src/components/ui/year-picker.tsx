import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface YearPickerProps {
  value?: number;
  onChange: (year: number) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function YearPicker({
  value,
  onChange,
  className,
  placeholder = "Select birth year",
  disabled = false,
}: YearPickerProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Generate years from 1924 (100 years old) to 2006 (18 years old)
  const currentYear = new Date().getFullYear();
  const years: number[] = [];
  for (let year = currentYear - 18; year >= currentYear - 100; year--) {
    years.push(year);
  }

  const handleYearSelect = (year: number) => {
    onChange(year);
    setIsOpen(false);
  };

  const getAgeFromYear = (birthYear: number) => {
    return currentYear - birthYear;
  };

  return (
    <div className={cn("relative", className)}>
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          "flex h-12 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          "placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "hover:bg-accent hover:text-accent-foreground",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
      >
        <span className={cn(!value && "text-muted-foreground")}>
          {value || placeholder}
        </span>
        <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />

          {/* Dropdown */}
          <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border bg-popover p-1 text-popover-foreground shadow-md">
            <div className="grid gap-1">
              {years.map((year) => (
                <button
                  key={year}
                  type="button"
                  onClick={() => handleYearSelect(year)}
                  className={cn(
                    "relative flex cursor-pointer select-none items-center justify-between rounded-sm px-2 py-1.5 text-sm outline-none",
                    "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                    value === year && "bg-accent text-accent-foreground"
                  )}
                >
                  <span>{year}</span>
                </button>
              ))}
            </div>

            {years.length === 0 && (
              <div className="py-6 text-center text-sm text-muted-foreground">
                No years available
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
