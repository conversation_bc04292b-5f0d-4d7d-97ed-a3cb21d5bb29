import { Link } from "@tanstack/react-router";
import { Menu, X } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navLinks = [
    { href: "/image-analyzer", label: "Image Analyzer" },
    { href: "/bio-analyzer", label: "Bio Analyzer" },
    { href: "/dashboard", label: "Dashboard" },
  ];

  return (
    <nav className="sticky top-0 z-50 w-full border-b border-cloud-white bg-cloud-white/70 backdrop-blur-md">
      <div className=" mx-auto px-4 md:px-6">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="flex items-center justify-center p-2">
              <img
                src="/tinderOptimizer256x256.avif"
                alt="Tinder Optimizer"
                className="h-12 w-12 object-contain"
              />
            </div>
            <span
              className="text-4xl font-bold text-graphite-90 !-ml-2
"
            >
              Tinder AI Optimizer
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                to={link.href}
                className="text-body-md text-graphite-60 hover:text-flame-red transition-colors"
                activeProps={{
                  className: "text-flame-red font-medium",
                }}
              >
                {link.label}
              </Link>
            ))}
            <Button asChild size="lg" variant="primary">
              <Link to="/welcome">Get Started</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 text-graphite-60 hover:text-graphite-90 transition-colors"
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            "md:hidden overflow-hidden transition-all duration-300 ease-in-out",
            isMobileMenuOpen ? "max-h-64" : "max-h-0"
          )}
        >
          <div className="py-4 space-y-3">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                to={link.href}
                className="block text-body-md text-graphite-60 hover:text-flame-red transition-colors py-2"
                activeProps={{
                  className: "text-flame-red font-medium",
                }}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
            <Button asChild size="default" variant="primary" className="w-full">
              <Link to="/welcome" onClick={() => setIsMobileMenuOpen(false)}>
                Get Started
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}
