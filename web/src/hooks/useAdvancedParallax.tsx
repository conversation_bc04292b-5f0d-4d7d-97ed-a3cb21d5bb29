import { useEffect, useRef, useState } from "react";

interface AdvancedParallaxOptions {
  speed?: number;
  offset?: number;
  rotation?: boolean;
  scale?: boolean;
  opacity?: boolean;
  perspective?: number;
  easing?: string;
}

export function useAdvancedParallax<T extends HTMLElement>(
  options: AdvancedParallaxOptions = {}
) {
  const {
    speed = 1,
    offset = 0,
    rotation = false,
    scale = false,
    opacity = false,
    perspective = 1000,
    easing = "cubic-bezier(0.25, 0.46, 0.45, 0.94)"
  } = options;

  const ref = useRef<T>(null);
  const [state, setState] = useState({
    transform: "",
    opacity: 1,
  });

  useEffect(() => {
    let ticking = false;

    const updateTransform = () => {
      if (!ref.current) return;

      const element = ref.current;
      const rect = element.getBoundingClientRect();
      const scrolled = window.scrollY;
      const windowHeight = window.innerHeight;
      const elementTop = rect.top + scrolled;
      const elementHeight = rect.height;
      const elementCenter = elementTop + elementHeight / 2;
      const windowCenter = scrolled + windowHeight / 2;
      
      // Calculate progress (0 to 1) of element through viewport
      const progress = (windowCenter - elementTop) / (windowHeight + elementHeight);
      const clampedProgress = Math.max(0, Math.min(1, progress));
      
      // Parallax calculations
      const yOffset = (windowCenter - elementCenter) * speed * 0.5;
      const zOffset = Math.abs(windowCenter - elementCenter) * 0.2;
      
      let transformString = `perspective(${perspective}px)`;
      
      // Y-axis parallax
      transformString += ` translateY(${yOffset + offset}px)`;
      
      // Z-axis depth
      transformString += ` translateZ(${-zOffset}px)`;
      
      // Rotation based on scroll
      if (rotation) {
        const rotateX = (progress - 0.5) * 30 * speed;
        const rotateY = Math.sin(progress * Math.PI) * 10;
        transformString += ` rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
      }
      
      // Scale based on distance from center
      if (scale) {
        const scaleValue = 0.8 + (clampedProgress * 0.4);
        transformString += ` scale(${scaleValue})`;
      }
      
      // Opacity fade
      let opacityValue = 1;
      if (opacity) {
        if (progress < 0.2) {
          opacityValue = progress * 5;
        } else if (progress > 0.8) {
          opacityValue = (1 - progress) * 5;
        }
      }
      
      setState({
        transform: transformString,
        opacity: opacityValue,
      });
      
      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(updateTransform);
        ticking = true;
      }
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("resize", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, [speed, offset, rotation, scale, opacity, perspective]);

  return { ref, ...state, style: { transform: state.transform, opacity: state.opacity, transition: `all 0.1s ${easing}` } };
}