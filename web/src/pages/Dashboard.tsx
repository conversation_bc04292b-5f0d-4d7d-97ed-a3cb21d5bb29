import { User<PERSON><PERSON><PERSON>, useUser } from "@clerk/tanstack-react-start";
import { <PERSON> } from "@tanstack/react-router";
import { Camera, ChevronDown, ChevronUp, FileText, Flame, Menu, Star, X } from "lucide-react";
import { useEffect, useState } from "react";
import { ActionCard } from "@/components/ui/action-card";
import { AnalysisHistoryItem } from "@/components/ui/analysis-history-item";
import { Button } from "@/components/ui/button";
import { CircularProgress } from "@/components/ui/circular-progress";
import { ScoreCard } from "@/components/ui/score-card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UpgradePrompt } from "@/components/ui/upgrade-prompt";

export function Dashboard() {
  const { user } = useUser();
  const [historyExpanded, setHistoryExpanded] = useState(false);
  const [quickTipExpanded, setQuickTipExpanded] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Mock data - replace with real data from your backend
  const analysisHistory = [
    { service: "Bio Analyzer", date: "2025-07-12", score: 70 },
    { service: "Photos Analyzer", date: "2025-07-11", score: 60 },
    { service: "Bio Analyzer", date: "2025-07-10", score: 68 },
  ];

  // Close mobile menu when clicking outside or pressing escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when mobile menu is open
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [mobileMenuOpen]);

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Navigation Header */}
      <div className="border-b border-slate-200 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 py-4">
          <div className="flex items-center justify-between min-w-0 gap-4">
            {/* Logo and Navigation */}
            <div className="flex items-center gap-2 sm:gap-8 min-w-0 flex-1 mr-2">
              <div className="flex items-center gap-2 flex-shrink-0">
                <Flame className="h-5 w-5 sm:h-6 sm:w-6 text-flame-red" />
                <span className="text-base sm:text-lg lg:text-xl font-bold text-slate-900 truncate">
                  TinderOP
                </span>
              </div>

              {/* Desktop Navigation */}
              <Tabs defaultValue="dashboard" className="w-auto hidden sm:block flex-shrink-0">
                <TabsList className="bg-transparent p-0 h-auto">
                  <TabsTrigger
                    value="dashboard"
                    className="px-2 sm:px-3 lg:px-4 py-2 text-sm font-medium text-slate-600 data-[state=active]:text-slate-900 data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-slate-900 rounded-none whitespace-nowrap"
                  >
                    Dashboard
                  </TabsTrigger>
                  <TabsTrigger
                    value="bio"
                    className="px-2 sm:px-3 lg:px-4 py-2 text-sm font-medium text-slate-600 data-[state=active]:text-slate-900 data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-slate-900 rounded-none whitespace-nowrap"
                  >
                    Bio Analyzer
                  </TabsTrigger>
                  <TabsTrigger
                    value="photos"
                    className="px-2 sm:px-3 lg:px-4 py-2 text-sm font-medium text-slate-600 data-[state=active]:text-slate-900 data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-slate-900 rounded-none whitespace-nowrap"
                  >
                    Photos Analyzer
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Right side actions */}
            <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
              <Button
                size="sm"
                className="bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90 text-white text-xs px-3 py-2 h-8 whitespace-nowrap hidden sm:flex"
              >
                <Star className="h-3 w-3 mr-1.5 flex-shrink-0" />
                Upgrade Now
              </Button>

              {/* Mobile Menu Button */}
              <button
                className="sm:hidden p-2 text-slate-600 hover:text-slate-900 rounded-md hover:bg-slate-100 transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </button>

              <UserButton />
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          {mobileMenuOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 bg-black/50 z-40 sm:hidden"
                onClick={() => setMobileMenuOpen(false)}
              />

              {/* Menu */}
              <div className="sm:hidden border-t border-slate-200 bg-white relative z-50">
                <div className="px-4 py-3 space-y-1">
                  <Link
                    to="/dashboard"
                    className="block px-3 py-3 text-sm font-medium text-slate-900 bg-slate-100 rounded-md min-h-[44px] flex items-center"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/bio-analyzer"
                    className="block px-3 py-3 text-sm font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-md min-h-[44px] flex items-center transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Bio Analyzer
                  </Link>
                  <Link
                    to="/image-analyzer"
                    className="block px-3 py-3 text-sm font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-md min-h-[44px] flex items-center transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Photos Analyzer
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8 overflow-x-hidden">
        {/* Profile Optimization Score Section */}
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-xl sm:text-2xl font-bold text-slate-900 mb-2">
            Profile Optimization Score
          </h1>
          <p className="text-sm sm:text-base text-slate-600 mb-6 sm:mb-8">
            Your current score. Run analyzers to improve!
          </p>

          {/* Circular Progress */}
          <div className="flex justify-center mb-6 sm:mb-8">
            <div className="w-36 h-36 sm:w-40 sm:h-40">
              <CircularProgress
                value={65}
                size={160}
                strokeWidth={12}
                className="text-slate-200 w-full h-full"
              />
            </div>
          </div>

          {/* Score Bars */}
          <div className="max-w-sm sm:max-w-md mx-auto space-y-3 sm:space-y-4 mb-4 sm:mb-6">
            <ScoreCard label="Bio Score" value={70} />
            <ScoreCard label="Photos Score" value={60} />
          </div>

          {/* Upgrade Prompt */}
          <UpgradePrompt
            currentScore={65}
            premiumScore={88}
            className="max-w-sm sm:max-w-md mx-auto"
          />
        </div>

        {/* Quick Actions */}
        <div className="mb-8 sm:mb-12">
          <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-4 sm:mb-6">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <ActionCard
              title="Bio Analyzer"
              description="Get feedback on your bio."
              icon={FileText}
              lastScore={70}
              buttonText="Analyze Now"
              buttonVariant="default"
              onClick={() => {
                /* Navigate to bio analyzer */
              }}
            />
            <ActionCard
              title="Photos Analyzer"
              description="Rate your profile pictures."
              icon={Camera}
              lastScore={60}
              buttonText="Analyze Now"
              buttonVariant="default"
              onClick={() => {
                /* Navigate to photos analyzer */
              }}
            />
            <ActionCard
              title="Pro Bio Analyzer"
              description="+30% accuracy with AI."
              icon={Star}
              buttonText="Unlock Pro"
              buttonVariant="secondary"
              isPro={true}
              onClick={() => {
                /* Show upgrade modal */
              }}
            />
            <ActionCard
              title="Pro Photos Analyzer"
              description="AI-powered enhancements."
              icon={Star}
              buttonText="Unlock Pro"
              buttonVariant="secondary"
              isPro={true}
              onClick={() => {
                /* Show upgrade modal */
              }}
            />
          </div>
        </div>

        {/* Insights & History */}
        <div className="space-y-4 sm:space-y-6">
          <h2 className="text-lg sm:text-xl font-bold text-slate-900">Insights & History</h2>

          {/* Recent Analyses */}
          <div className="bg-white border border-slate-200 rounded-lg">
            <button
              onClick={() => setHistoryExpanded(!historyExpanded)}
              className="w-full px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between text-left hover:bg-slate-50 transition-colors"
            >
              <span className="font-semibold text-slate-900 text-sm sm:text-base">
                Recent Analyses
              </span>
              {historyExpanded ? (
                <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5 text-slate-500" />
              ) : (
                <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-slate-500" />
              )}
            </button>

            {historyExpanded && (
              <div className="px-4 sm:px-6 pb-3 sm:pb-4">
                {analysisHistory.map((item, index) => (
                  <AnalysisHistoryItem
                    key={index}
                    service={item.service}
                    date={item.date}
                    score={item.score}
                    onViewDetails={() => {
                      /* Show details modal */
                    }}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Quick Tip */}
          <div className="bg-white border border-slate-200 rounded-lg">
            <button
              onClick={() => setQuickTipExpanded(!quickTipExpanded)}
              className="w-full px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between text-left hover:bg-slate-50 transition-colors"
            >
              <span className="font-semibold text-slate-900 text-sm sm:text-base">Quick Tip</span>
              {quickTipExpanded ? (
                <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5 text-slate-500" />
              ) : (
                <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-slate-500" />
              )}
            </button>

            {quickTipExpanded && (
              <div className="px-4 sm:px-6 pb-3 sm:pb-4">
                <p className="text-xs sm:text-sm text-slate-600 leading-relaxed">
                  Pro tip: Upload photos with good lighting and genuine smiles to improve your
                  photos score. Keep your bio concise but engaging to boost your bio score.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
