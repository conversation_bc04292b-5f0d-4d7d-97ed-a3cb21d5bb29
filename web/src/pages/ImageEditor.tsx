import { Link } from "@tanstack/react-router";
import {
  Arrow<PERSON><PERSON><PERSON>,
  Check,
  ChevronDown,
  ChevronUp,
  Download,
  Edit3,
  FileUp,
  Loader2,
  RefreshC<PERSON>,
  Sparkles,
  Wand2,
  X,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { PrivacyNotice } from "@/components/PrivacyNotice";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { imageEditingService } from "@/lib/image-editing";
import {
  createImagePreview,
  imageStorage,
  initStorage,
  revokeImagePreview,
  type StoredImage,
  sessionManager,
} from "@/lib/storage";

type ImageWithPreview = {
  id: string;
  fileName: string;
  preview: string;
  storedImage: StoredImage;
};

type EditingProgress = {
  progress: number;
  status: string;
  partialImageUrl?: string;
};

type EditResult = {
  id: string;
  originalId: string;
  editedPreview: string;
  editedBlob: Blob;
  prompt: string;
  processingTime: number;
  success: boolean;
  error?: string;
  originalImagePreview?: string; // For before/after comparison
  originalImageBlob?: Blob;
};

const EditCard = ({
  result,
  originalImage,
  onDownload,
  onRetry,
  onRemove,
  versionInfo,
  isRetrying,
}: {
  result: EditResult;
  originalImage?: ImageWithPreview;
  onDownload: (blob: Blob, filename: string) => void;
  onRetry: (originalId: string, prompt: string) => void;
  onRemove: (editedImageId: string) => void;
  versionInfo?: { current: number; total: number };
  isRetrying?: boolean;
}) => {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);

    const container = containerRef.current;
    if (!container) {
      console.log("🔧 Slider: Container ref not found");
      return;
    }

    const rect = container.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    console.log(`🔧 Slider: Mouse down at ${percentage.toFixed(1)}%`);
    setSliderPosition(percentage);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      const container = containerRef.current;
      if (!container) {
        console.log("🔧 Slider: Container ref not found during move");
        return;
      }

      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      setSliderPosition(percentage);
    },
    [isDragging]
  );

  const handleMouseUp = useCallback(() => {
    console.log("🔧 Slider: Mouse up, stopping drag");
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
      {result.success ? (
        <div
          ref={containerRef}
          className="relative aspect-[7/10] overflow-hidden cursor-col-resize select-none"
          onMouseDown={handleMouseDown}
        >
          {/* Before/After Image Container */}
          <div className="relative w-full h-full">
            {/* Edited Image (Background) */}
            <img
              src={result.editedPreview}
              alt="Edited"
              className="absolute inset-0 w-full h-full object-cover pointer-events-none"
            />

            {/* Original Image (Clipped overlay) */}
            <div
              className="absolute inset-0 overflow-hidden pointer-events-none"
              style={{
                clipPath: `polygon(0 0, ${sliderPosition}% 0, ${sliderPosition}% 100%, 0 100%)`,
              }}
            >
              {result.originalImagePreview || originalImage?.preview ? (
                <img
                  src={result.originalImagePreview || originalImage?.preview || "/placeholder.svg"}
                  alt="Original"
                  className="w-full h-full object-cover pointer-events-none"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-xs mb-1">Original Unavailable</div>
                    <div className="text-xs opacity-75">Enhanced version preserved</div>
                  </div>
                </div>
              )}
            </div>

            {/* Slider Line */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10 pointer-events-none"
              style={{ left: `${sliderPosition}%` }}
            >
              <div className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-gray-400 rounded-full bg-white"></div>
              </div>
            </div>

            {/* Before/After Labels */}
            <div className="absolute top-3 left-3 z-20">
              <Badge variant="outline" className="bg-black/80 text-white border-white/30">
                {result.originalImagePreview || originalImage?.preview
                  ? "Before"
                  : "Original Unavailable"}
              </Badge>
            </div>
            <div className="absolute top-3 right-3 z-20">
              <Badge variant="outline" className="bg-black/80 text-white border-white/30">
                After
              </Badge>
            </div>
          </div>
        </div>
      ) : (
        <div className="aspect-[7/10] bg-gray-100 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <X className="h-8 w-8 mx-auto mb-2 text-error-crimson" />
            <p className="text-sm">Edit failed</p>
          </div>
        </div>
      )}

      <div className="p-4">
        {/* Version Info & Prompt */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-graphite-90">Edit Prompt:</h4>
            {versionInfo && versionInfo.total > 1 && (
              <Badge variant="outline" className="text-xs">
                Version {versionInfo.current} of {versionInfo.total}
              </Badge>
            )}
          </div>
          <p className="text-sm text-graphite-60 italic">"{result.prompt}"</p>
        </div>

        {/* Processing Info */}
        <div className="flex justify-between items-center mb-3 text-xs text-graphite-60">
          <span>Processing: {result.processingTime}ms</span>
          <span
            className={`font-medium ${result.success ? "text-success-green" : "text-error-crimson"}`}
          >
            {result.success ? "Success" : "Failed"}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <div className="flex gap-2">
            {result.success && (
              <Button
                onClick={() => onDownload(result.editedBlob, `edited_${Date.now()}.png`)}
                size="sm"
                variant="primary"
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            )}
            <Button
              onClick={() => onRetry(result.originalId, result.prompt)}
              size="sm"
              variant="ghost"
              className={`flex-1 transition-colors ${
                isRetrying ? "bg-blue-50 border-blue-300 text-blue-700 hover:bg-blue-100" : ""
              }`}
              disabled={(!originalImage && !result.originalImageBlob) || isRetrying}
              title={
                isRetrying
                  ? "Creating new AI enhancement..."
                  : originalImage || result.originalImageBlob
                    ? "Create a new AI-enhanced version with the same prompt"
                    : "Cannot retry - original image unavailable"
              }
            >
              <RefreshCw
                className={`h-4 w-4 mr-1 ${isRetrying ? "animate-spin text-blue-600" : ""}`}
              />
              {isRetrying
                ? "Creating..."
                : originalImage || result.originalImageBlob
                  ? "Try Again"
                  : "Original Unavailable"}
            </Button>
          </div>

          {/* Remove Button */}
          <Button
            onClick={() => onRemove(result.id)}
            size="sm"
            variant="ghost"
            className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <X className="h-4 w-4 mr-1" />
            Remove This Version
          </Button>
        </div>

        {/* Error Message */}
        {result.error && (
          <div className="mt-3 p-2 bg-error-crimson/10 rounded text-sm text-error-crimson">
            {result.error}
          </div>
        )}
      </div>
    </div>
  );
};

const EditingCard = ({
  image,
  progress,
}: {
  image: ImageWithPreview;
  progress?: EditingProgress;
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const estimatedTotal = 110; // 110 seconds as mentioned
  const timeProgress = Math.min((elapsedTime / estimatedTotal) * 100, 95);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="relative">
        {/* Show streaming preview if available, otherwise original */}
        <img
          src={progress?.partialImageUrl || image.preview}
          alt={image.fileName}
          className="w-full h-48 object-cover transition-all duration-500"
        />

        {/* Processing overlay - only show when no partial preview */}
        {!progress?.partialImageUrl && (
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 text-center">
              <Sparkles className="h-6 w-6 mx-auto mb-1 text-flame-red animate-pulse" />
              <div className="text-xs font-medium text-graphite-90">AI Processing</div>
            </div>
          </div>
        )}

        {/* Streaming indicator when showing partial preview */}
        {progress?.partialImageUrl && (
          <div className="absolute top-2 right-2">
            <div className="bg-flame-red/90 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              LIVE
            </div>
          </div>
        )}
      </div>

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-graphite-90 truncate">{image.fileName}</h4>
          <div className="text-xs text-graphite-60 font-mono">
            {formatTime(elapsedTime)} / ~{formatTime(estimatedTotal)}
          </div>
        </div>

        {progress && (
          <div className="space-y-3">
            {/* Main progress */}
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-graphite-60">AI Processing</span>
                <span className="text-flame-red font-medium">{Math.round(progress.progress)}%</span>
              </div>
              <Progress value={progress.progress} className="h-2" />
            </div>

            {/* Time-based progress */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-graphite-50">Time Progress</span>
                <span className="text-graphite-50">{Math.round(timeProgress)}%</span>
              </div>
              <Progress value={timeProgress} className="h-1 opacity-60" />
            </div>

            {/* Status with animated dots */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-graphite-90 font-medium flex items-center">
                {progress.status}
                <span className="ml-1 animate-pulse">
                  <span className="animate-bounce">.</span>
                  <span className="animate-bounce" style={{ animationDelay: "0.1s" }}>
                    .
                  </span>
                  <span className="animate-bounce" style={{ animationDelay: "0.2s" }}>
                    .
                  </span>
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="mt-4 flex items-center text-sm text-graphite-60">
          <Wand2 className="h-4 w-4 mr-2 text-flame-red animate-spin" />
          <span>Enhancing with AI magic...</span>
        </div>

        {/* Processing stages indicator */}
        <div className="mt-3 flex justify-between text-xs text-graphite-40">
          <span className={elapsedTime > 10 ? "text-success-green" : "text-flame-red"}>
            ● Analysis
          </span>
          <span
            className={
              elapsedTime > 30
                ? "text-success-green"
                : elapsedTime > 10
                  ? "text-flame-red"
                  : "text-graphite-40"
            }
          >
            ● Enhancement
          </span>
          <span
            className={
              elapsedTime > 60
                ? "text-success-green"
                : elapsedTime > 30
                  ? "text-flame-red"
                  : "text-graphite-40"
            }
          >
            ● Refinement
          </span>
          <span
            className={
              elapsedTime > 90
                ? "text-success-green"
                : elapsedTime > 60
                  ? "text-flame-red"
                  : "text-graphite-40"
            }
          >
            ● Finalization
          </span>
        </div>
      </div>
    </div>
  );
};

export function ImageEditor() {
  const [images, setImages] = useState<ImageWithPreview[]>([]);
  const [results, setResults] = useState<EditResult[]>([]);
  const [status, setStatus] = useState<"idle" | "processing" | "done">("idle");
  const [progress, setProgress] = useState<Record<string, EditingProgress>>({});
  const [isStorageReady, setIsStorageReady] = useState(false);
  const [editPrompt, setEditPrompt] = useState("");
  const [retryingCards, setRetryingCards] = useState<Set<string>>(new Set());
  const [showOriginals, setShowOriginals] = useState(false);
  const [globalImageCount, setGlobalImageCount] = useState(0);
  const [selectedForEdit, setSelectedForEdit] = useState<Set<string>>(new Set());

  // Auto-expand originals when no enhanced results exist
  useEffect(() => {
    const hasSuccessfulResults = results.some((result) => result.success);
    if (!hasSuccessfulResults) {
      setShowOriginals(true);
    } else {
      setShowOriginals(false);
    }
  }, [results]);

  // Update global image count
  const updateGlobalCount = useCallback(async () => {
    if (isStorageReady) {
      const count = await imageStorage.getGlobalOriginalCount();
      setGlobalImageCount(count);
      console.log(`📊 Global Count Updated: ${count}/${MAX_IMAGES}`);
    }
  }, [isStorageReady]);

  // Update global count when storage is ready or images change
  useEffect(() => {
    updateGlobalCount();
  }, [updateGlobalCount, images]);

  // Limit to 9 images as specified - global hard limit
  const MAX_IMAGES = 9;

  useEffect(() => {
    initStorage().then(() => {
      setIsStorageReady(true);
      loadSessionImages();
    });

    return () => {
      // Clean up preview URLs
      images.forEach((img) => revokeImagePreview(img.preview));
      results.forEach((result) => {
        if (result.editedPreview) {
          revokeImagePreview(result.editedPreview);
        }
        if (result.originalImagePreview) {
          revokeImagePreview(result.originalImagePreview);
        }
      });
    };
  }, []);

  const loadSessionImages = async () => {
    try {
      const session = sessionManager.getCurrentSession();
      const storedImages = await imageStorage.getSessionImages(session.id);

      // Load original images (not edited versions) up to MAX_IMAGES
      const originalImages = storedImages.filter((img) => !img.isEdited).slice(0, MAX_IMAGES);

      const imagesWithPreviews: ImageWithPreview[] = originalImages.map((img) => ({
        id: img.id,
        fileName: img.fileName,
        preview: createImagePreview(img.blob),
        storedImage: img,
      }));

      setImages(imagesWithPreviews);

      // Load existing edited images and create results
      const editedImages = storedImages.filter((img) => img.isEdited);
      const existingResults: EditResult[] = [];

      for (const editedImg of editedImages) {
        if (editedImg.originalImageId) {
          const originalImg = originalImages.find((orig) => orig.id === editedImg.originalImageId);

          // Create result regardless of whether original still exists
          // Use stored original image blob if available, otherwise use current original
          const originalImageBlob = editedImg.originalImageBlob || originalImg?.blob;
          const originalImagePreview = originalImageBlob
            ? createImagePreview(originalImageBlob)
            : undefined;

          existingResults.push({
            id: editedImg.id,
            originalId: editedImg.originalImageId,
            editedPreview: createImagePreview(editedImg.blob),
            editedBlob: editedImg.blob,
            prompt: editedImg.editPrompt || "Unknown edit",
            processingTime: 0, // Historical data doesn't have processing time
            success: true,
            originalImagePreview,
            originalImageBlob,
          });
        }
      }

      if (existingResults.length > 0) {
        setResults(existingResults);
        setStatus("done");
        console.log(`📸 Loaded ${existingResults.length} existing edited images from storage`);
      }
    } catch (error) {
      console.error("Failed to load session images:", error);
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!isStorageReady) return;

      console.log(`📤 Upload: Attempting to upload ${acceptedFiles.length} files`);

      // Enforce global storage limit before adding new images
      const currentGlobalCount = await imageStorage.getGlobalOriginalCount();
      const totalAfterUpload = currentGlobalCount + acceptedFiles.length;

      console.log(
        `📊 Storage: Current global count: ${currentGlobalCount}, After upload: ${totalAfterUpload}, Limit: ${MAX_IMAGES}`
      );

      if (totalAfterUpload > MAX_IMAGES) {
        console.log(`🔧 Storage: Would exceed limit, enforcing global limit first`);
        // Calculate how many slots we need for new images
        const slotsNeeded = acceptedFiles.length;
        const targetCount = MAX_IMAGES - slotsNeeded;

        // Enforce limit to make room for new images
        await imageStorage.enforceGlobalOriginalLimit(Math.max(0, targetCount));
      }

      // Process all files (global limit enforcement already made room)
      const filesToProcess = acceptedFiles.slice(0, MAX_IMAGES);

      try {
        const session = sessionManager.getCurrentSession();
        const newImages: ImageWithPreview[] = [];

        for (const file of filesToProcess) {
          console.log(`📤 Upload: Storing image: ${file.name}`);
          const imageId = await imageStorage.storeImage(file, session.id);
          sessionManager.addImageToSession(imageId);

          newImages.push({
            id: imageId,
            fileName: file.name,
            preview: URL.createObjectURL(file),
            storedImage: {
              id: imageId,
              fileName: file.name,
              blob: file,
              mimeType: file.type,
              uploadedAt: Date.now(),
              sessionId: session.id,
            },
          });
        }

        console.log(`✅ Upload: Successfully uploaded ${newImages.length} images`);
        setImages((prev) => [...prev, ...newImages]);

        // Auto-select new images for editing
        const newImageIds = newImages.map((img) => img.id);
        setSelectedForEdit((prev) => new Set([...prev, ...newImageIds]));
        console.log(
          `🎯 Edit Selection: Auto-selected ${newImageIds.length} new images for editing`
        );

        // Log final storage state
        const finalCount = await imageStorage.getGlobalOriginalCount();
        console.log(`📊 Storage: Final global count: ${finalCount}/${MAX_IMAGES}`);
      } catch (error) {
        console.error("Failed to process files:", error);
      }
    },
    [images.length, isStorageReady]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpg", ".jpeg", ".png", ".webp"],
    },
    multiple: true,
    disabled: !isStorageReady || globalImageCount >= MAX_IMAGES || status === "processing",
  });

  // Remove image from edit selection (not from storage)
  const removeFromEditSelection = (imageId: string) => {
    const image = images.find((img) => img.id === imageId);
    if (image) {
      const newSelected = new Set(selectedForEdit);
      newSelected.delete(imageId);
      setSelectedForEdit(newSelected);
      console.log(`🚫 Removed ${image.fileName} from edit selection (kept in storage)`);
    }
  };

  // Permanently delete image from storage
  const removeImage = async (imageId: string) => {
    const image = images.find((img) => img.id === imageId);
    if (image) {
      revokeImagePreview(image.preview);
    }

    // Remove from UI state and edit selection
    setImages((prev) => prev.filter((img) => img.id !== imageId));
    setSelectedForEdit((prev) => {
      const newSelected = new Set(prev);
      newSelected.delete(imageId);
      return newSelected;
    });

    try {
      // Delete only the original image from storage
      await imageStorage.deleteImage(imageId);
      sessionManager.removeImageFromSession(imageId);

      console.log(`🗑️ Permanently deleted original image ${imageId}. Edited versions preserved.`);

      // Update global count after removal
      await updateGlobalCount();
    } catch (error) {
      console.error("Failed to delete image:", error);
    }
  };

  const handleEditImages = async () => {
    const selectedImages = images.filter((img) => selectedForEdit.has(img.id));
    if (selectedImages.length === 0 || !editPrompt.trim()) return;

    console.log(
      `🎯 Edit: Processing ${selectedImages.length} selected images out of ${images.length} total`
    );
    setStatus("processing");
    // Don't clear existing results - we want to append new ones

    try {
      // Process only selected images concurrently with streaming
      const editPromises = selectedImages.map(async (image) => {
        setProgress((prev) => ({
          ...prev,
          [image.id]: { progress: 0, status: "Starting streaming edit..." },
        }));

        try {
          // Use the streaming API
          const streamGenerator = imageEditingService.editSingleImageStream(
            image.storedImage.blob,
            editPrompt
          );

          let finalResult: any = null;

          // Process streaming updates
          for await (const update of streamGenerator) {
            setProgress((prev) => ({
              ...prev,
              [image.id]: {
                progress: update.progress,
                status: update.status,
                partialImageUrl: update.partialImageUrl,
              },
            }));

            if (update.isComplete && update.finalResult) {
              finalResult = update.finalResult;
              break;
            }
          }

          // Store edited image if successful
          let editResult: EditResult;

          if (finalResult?.success) {
            const session = sessionManager.getCurrentSession();
            const editedImageId = await imageStorage.storeEditedImage(
              finalResult.editedImageBlob,
              image.id,
              editPrompt,
              session.id,
              image.storedImage.blob // Pass original image blob
            );

            editResult = {
              id: editedImageId,
              originalId: image.id,
              editedPreview: finalResult.editedImageUrl,
              editedBlob: finalResult.editedImageBlob,
              prompt: editPrompt,
              processingTime: finalResult.processingTime,
              success: true,
              originalImagePreview: image.preview,
              originalImageBlob: image.storedImage.blob,
            };
          } else {
            editResult = {
              id: `failed_${image.id}`,
              originalId: image.id,
              editedPreview: "",
              editedBlob: new Blob(),
              prompt: editPrompt,
              processingTime: finalResult?.processingTime || 0,
              success: false,
              error: finalResult?.error || "Streaming failed",
            };
          }

          setResults((prev) => [...prev, editResult]);

          // Clear progress for this image
          setProgress((prev) => {
            const updated = { ...prev };
            delete updated[image.id];
            return updated;
          });

          return editResult;
        } catch (error) {
          console.error(`Failed to edit image ${image.id}:`, error);

          const errorResult: EditResult = {
            id: `error_${image.id}`,
            originalId: image.id,
            editedPreview: "",
            editedBlob: new Blob(),
            prompt: editPrompt,
            processingTime: 0,
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          };

          setResults((prev) => [...prev, errorResult]);

          // Clear progress for this image
          setProgress((prev) => {
            const updated = { ...prev };
            delete updated[image.id];
            return updated;
          });

          return errorResult;
        }
      });

      // Wait for all images to complete
      await Promise.all(editPromises);
    } catch (error) {
      console.error("Batch editing failed:", error);
    } finally {
      setStatus("done");
    }
  };

  const removeEditedImage = async (editedImageId: string) => {
    const editedResult = results.find((result) => result.id === editedImageId);
    if (editedResult) {
      if (editedResult.editedPreview) {
        revokeImagePreview(editedResult.editedPreview);
      }
      if (editedResult.originalImagePreview) {
        revokeImagePreview(editedResult.originalImagePreview);
      }
    }

    // Remove from UI state
    setResults((prev) => prev.filter((result) => result.id !== editedImageId));

    try {
      // Delete edited image from storage
      await imageStorage.deleteImage(editedImageId);
      console.log(`🗑️ Removed edited image ${editedImageId}`);
    } catch (error) {
      console.error("Failed to delete edited image:", error);
    }
  };

  const handleDownload = (blob: Blob, filename: string) => {
    imageEditingService.downloadImage(blob, filename);
  };

  const handleRetry = async (originalId: string, prompt: string) => {
    const image = images.find((img) => img.id === originalId);

    // If original image is not in UI, try to get it from existing results
    let originalImageBlob: Blob | undefined;
    if (image) {
      originalImageBlob = image.storedImage.blob;
    } else {
      // Look for stored original in existing results
      const existingResult = results.find(
        (r) => r.originalId === originalId && r.originalImageBlob
      );
      originalImageBlob = existingResult?.originalImageBlob;
    }

    if (!originalImageBlob) {
      console.error("Cannot retry: original image blob not available");
      return;
    }

    // Mark this original ID as being retried
    setRetryingCards((prev) => new Set(prev).add(originalId));

    try {
      // Generate unique progress key for this retry attempt
      const retryKey = `${originalId}_retry_${Date.now()}`;

      setProgress((prev) => ({
        ...prev,
        [retryKey]: { progress: 0, status: "Creating new AI enhancement..." },
      }));

      // Use streaming API for retry
      const streamGenerator = imageEditingService.editSingleImageStream(originalImageBlob, prompt);

      let finalResult: any = null;

      // Process streaming updates
      for await (const update of streamGenerator) {
        setProgress((prev) => ({
          ...prev,
          [retryKey]: {
            progress: update.progress,
            status: update.status,
            partialImageUrl: update.partialImageUrl,
          },
        }));

        if (update.isComplete && update.finalResult) {
          finalResult = update.finalResult;
          break;
        }
      }

      // Count existing versions for this original image
      const existingVersions = results.filter((r) => r.originalId === originalId && r.success);
      const versionNumber = existingVersions.length + 1;

      // Create NEW edited version (don't overwrite existing)
      let newEditResult: EditResult;

      if (finalResult?.success) {
        const session = sessionManager.getCurrentSession();
        const editedImageId = await imageStorage.storeEditedImage(
          finalResult.editedImageBlob,
          originalId,
          prompt,
          session.id,
          originalImageBlob // Pass original image blob
        );

        newEditResult = {
          id: editedImageId,
          originalId,
          editedPreview: finalResult.editedImageUrl,
          editedBlob: finalResult.editedImageBlob,
          prompt,
          processingTime: finalResult.processingTime,
          success: true,
          originalImagePreview: image?.preview || createImagePreview(originalImageBlob),
          originalImageBlob,
        };

        console.log(
          `✨ Created new AI enhancement (version ${versionNumber}) for image ${originalId}`
        );
      } else {
        newEditResult = {
          id: `failed_${originalId}_${Date.now()}`,
          originalId,
          editedPreview: "",
          editedBlob: new Blob(),
          prompt,
          processingTime: finalResult?.processingTime || 0,
          success: false,
          error: finalResult?.error || "Streaming retry failed",
        };
      }

      // ADD new result instead of replacing existing one
      setResults((prev) => [...prev, newEditResult]);

      setProgress((prev) => {
        const updated = { ...prev };
        delete updated[retryKey];
        return updated;
      });

      // Show success message
      console.log(
        `✨ New AI enhancement created successfully! Version ${versionNumber} for image ${originalId}`
      );
    } catch (error) {
      console.error("Retry failed:", error);
    } finally {
      // Remove from retrying state
      setRetryingCards((prev) => {
        const updated = new Set(prev);
        updated.delete(originalId);
        return updated;
      });
    }
  };

  const clearAllImages = async () => {
    try {
      // Clean up preview URLs
      images.forEach((img) => revokeImagePreview(img.preview));
      results.forEach((result) => {
        if (result.editedPreview) {
          revokeImagePreview(result.editedPreview);
        }
      });

      // Clear UI state
      setImages([]);
      setResults([]);
      setStatus("idle");
      setProgress({});

      // Clear from storage
      const session = sessionManager.getCurrentSession();
      await imageStorage.clearSession(session.id);
      sessionManager.clearSession();

      console.log("🧹 Cleared all original and edited images");
    } catch (error) {
      console.error("Failed to clear all images:", error);
    }
  };

  const clearOnlyOriginalImages = async () => {
    try {
      // Clean up preview URLs for original images only
      images.forEach((img) => revokeImagePreview(img.preview));

      // Clear only original images from UI state
      setImages([]);

      // Delete only original images from storage
      const session = sessionManager.getCurrentSession();
      for (const image of images) {
        await imageStorage.deleteImage(image.id);
        sessionManager.removeImageFromSession(image.id);
      }

      console.log("🧹 Cleared only original images, preserved edited versions");
    } catch (error) {
      console.error("Failed to clear original images:", error);
    }
  };

  const suggestedPrompts = imageEditingService.getSuggestedPrompts();

  return (
    <div className="min-h-screen bg-cloud-white">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-h1 font-semibold text-graphite-90">AI Image Editor</h1>
              <p className="text-body-md text-graphite-60">
                Upload up to {MAX_IMAGES} images and enhance them with AI
              </p>
            </div>
          </div>
        </div>

        <PrivacyNotice />

        {/* Upload Area */}
        <div
          {...getRootProps()}
          className={`mb-8 p-8 border-2 border-dashed rounded-lg text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-flame-red bg-flame-red/5"
              : status === "processing"
                ? "border-gray-300 bg-gray-50 cursor-not-allowed"
                : globalImageCount >= MAX_IMAGES
                  ? "border-gray-300 bg-gray-50 cursor-not-allowed"
                  : "border-gray-300 hover:border-flame-red hover:bg-flame-red/5"
          }`}
        >
          <input {...getInputProps()} />
          <FileUp className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          {status === "processing" ? (
            <div>
              <Loader2 className="h-8 w-8 mx-auto mb-3 animate-spin text-flame-red" />
              <p className="text-body-md text-graphite-90 mb-1">
                Processing {images.length} image{images.length > 1 ? "s" : ""}
                ...
              </p>
              <p className="text-caption text-graphite-60">
                Upload will be available when processing completes
              </p>
            </div>
          ) : globalImageCount >= MAX_IMAGES ? (
            <div>
              <p className="text-body-md text-gray-500 mb-2">
                Maximum {MAX_IMAGES} images reached globally
              </p>
              <p className="text-caption text-gray-400">Remove some images to upload more</p>
            </div>
          ) : isDragActive ? (
            <p className="text-body-md text-flame-red">Drop images here...</p>
          ) : (
            <div>
              <p className="text-body-md text-graphite-90 mb-2">Upload images to edit with AI</p>
              <p className="text-caption text-graphite-60">
                Drag & drop images or click to select • Up to {MAX_IMAGES} images • JPG, PNG, WebP
              </p>
            </div>
          )}
        </div>

        {/* Edit Prompt Section */}
        {images.length > 0 && (
          <div className="mb-8 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-h2 font-semibold text-graphite-90 mb-4">Edit Instructions</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-graphite-90 mb-2">
                Describe how you want to edit your images:
              </label>
              <Textarea
                placeholder="e.g., Make the lighting more flattering and professional..."
                value={editPrompt}
                onChange={(e) => setEditPrompt(e.target.value)}
                className="min-h-[100px]"
              />
            </div>

            {/* Suggested Prompts */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-graphite-90 mb-2">
                Suggested prompts:
              </label>
              <div className="flex flex-wrap gap-2">
                {suggestedPrompts.map((prompt, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setEditPrompt(prompt)}
                    className="text-xs"
                  >
                    {prompt}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <Button
                onClick={handleEditImages}
                disabled={
                  !editPrompt.trim() || status === "processing" || selectedForEdit.size === 0
                }
                className="w-full"
              >
                {status === "processing" ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Editing {selectedForEdit.size} Image
                    {selectedForEdit.size > 1 ? "s" : ""}...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Edit {selectedForEdit.size} Selected Image
                    {selectedForEdit.size > 1 ? "s" : ""} with AI
                  </>
                )}
              </Button>

              {!editPrompt.trim() && selectedForEdit.size > 0 && (
                <p className="text-xs text-graphite-50 text-center">
                  ⏱️ Processing time: ~{selectedForEdit.size * 2} minutes for {selectedForEdit.size}{" "}
                  selected image
                  {selectedForEdit.size > 1 ? "s" : ""}
                </p>
              )}

              {selectedForEdit.size === 0 && images.length > 0 && (
                <p className="text-xs text-orange-600 text-center">
                  ⚠️ No images selected for editing. Click checkboxes to select images.
                </p>
              )}
            </div>
          </div>
        )}

        {/* Processing Images */}
        {status === "processing" && images.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-h2 font-semibold text-graphite-90">Processing Images</h3>
              <div className="flex items-center gap-2 text-sm text-graphite-60">
                <Loader2 className="h-4 w-4 animate-spin text-flame-red" />
                <span>
                  AI is working on {Object.keys(progress).length} image
                  {Object.keys(progress).length > 1 ? "s" : ""}
                </span>
              </div>
            </div>

            {/* Processing info banner */}
            <div className="bg-flame-red/5 border border-flame-red/20 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <Sparkles className="h-5 w-5 text-flame-red mt-0.5 animate-pulse" />
                <div>
                  <h4 className="font-medium text-graphite-90 mb-1">AI Enhancement in Progress</h4>
                  <p className="text-sm text-graphite-60 mb-2">
                    Our advanced AI is analyzing and enhancing your images. This process typically
                    takes 1-2 minutes per image for the best results.
                  </p>
                  <div className="flex items-center gap-4 text-xs text-graphite-50">
                    <span>• Deep image analysis</span>
                    <span>• Professional enhancement</span>
                    <span>• Quality optimization</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {images.map(
                (image) =>
                  progress[image.id] && (
                    <EditingCard key={image.id} image={image} progress={progress[image.id]} />
                  )
              )}
            </div>
          </div>
        )}

        {/* Original Images Section - Collapsible when AI enhanced versions exist */}
        {images.length > 0 && status !== "processing" && (
          <div className="mb-8">
            {(() => {
              const hasSuccessfulResults = results.some((result) => result.success);
              console.log(
                `🖼️ Original Images: images=${images.length}, hasSuccessfulResults=${hasSuccessfulResults}, showOriginals=${showOriginals}`
              );

              return (
                <div
                  className={`flex justify-between items-center mb-4 ${hasSuccessfulResults ? "cursor-pointer" : ""}`}
                  onClick={
                    hasSuccessfulResults ? () => setShowOriginals(!showOriginals) : undefined
                  }
                >
                  <div className="flex items-center gap-3">
                    <h3
                      className={`text-h2 font-semibold ${hasSuccessfulResults ? "text-graphite-70" : "text-graphite-90"}`}
                    >
                      Your Original Images ({globalImageCount}/{MAX_IMAGES} global)
                    </h3>
                    {hasSuccessfulResults && (
                      <div className="flex items-center gap-2">
                        {showOriginals ? (
                          <ChevronUp className="h-5 w-5 text-graphite-60" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-graphite-60" />
                        )}
                        <span className="text-sm text-graphite-60">
                          {showOriginals ? "Click to collapse" : "Click to expand"}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}

            {(() => {
              const hasSuccessfulResults = results.some((result) => result.success);
              const shouldShowContent = !hasSuccessfulResults || showOriginals;

              if (!shouldShowContent) return null;

              return (
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => {
                        const allImageIds = images.map((img) => img.id);
                        const allSelected = allImageIds.every((id) => selectedForEdit.has(id));

                        if (allSelected) {
                          // Deselect all
                          setSelectedForEdit(new Set());
                          console.log(`❌ Deselected all ${images.length} images from editing`);
                        } else {
                          // Select all
                          setSelectedForEdit(new Set(allImageIds));
                          console.log(`✅ Selected all ${images.length} images for editing`);
                        }
                      }}
                      variant="outline"
                      size="sm"
                      className="gap-2"
                    >
                      {images.every((img) => selectedForEdit.has(img.id)) ? (
                        <>
                          <X className="h-4 w-4" />
                          Deselect All
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4" />
                          Select All
                        </>
                      )}
                    </Button>
                    <span className="text-sm text-graphite-60">
                      {selectedForEdit.size} of {images.length} selected for editing
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    {results.length > 0 && (
                      <Badge variant="outline" className="gap-2">
                        <Sparkles className="h-4 w-4" />
                        {results.length} Edited
                      </Badge>
                    )}
                    <div className="flex flex-col gap-2">
                      <Button
                        onClick={clearOnlyOriginalImages}
                        variant="outline"
                        size="sm"
                        className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                        title="Remove only original images, keep AI-enhanced versions"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Clear Originals
                      </Button>
                      <Button
                        onClick={clearAllImages}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Remove all images including AI-enhanced versions"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Clear All
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })()}

            {(() => {
              const hasSuccessfulResults = results.some((result) => result.success);
              const shouldShowContent = !hasSuccessfulResults || showOriginals;

              if (!shouldShowContent) return null;

              return (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {images.map((image) => {
                    const editedVersions = results.filter(
                      (result) => result.originalId === image.id && result.success
                    );
                    const hasEditedVersions = editedVersions.length > 0;

                    return (
                      <div
                        key={image.id}
                        className={`relative bg-white rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg ${
                          selectedForEdit.has(image.id)
                            ? "shadow-[0_0_40px_rgba(255,88,81,0.6),0_0_80px_rgba(255,138,128,0.4)]"
                            : "shadow-md"
                        }`}
                        onClick={() => {
                          const newSelected = new Set(selectedForEdit);
                          if (selectedForEdit.has(image.id)) {
                            newSelected.delete(image.id);
                            console.log(`❌ Deselected image from editing: ${image.fileName}`);
                          } else {
                            newSelected.add(image.id);
                            console.log(`✅ Selected image for editing: ${image.fileName}`);
                          }
                          setSelectedForEdit(newSelected);
                        }}
                      >
                        <img
                          src={image.preview}
                          alt={image.fileName}
                          className="w-full h-48 object-cover"
                        />
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-semibold text-graphite-90 truncate flex-1">
                              {image.fileName}
                            </p>
                          </div>
                          {hasEditedVersions && (
                            <div className="flex items-center justify-between mt-1">
                              <div className="flex items-center gap-1">
                                <Sparkles className="h-3 w-3 text-flame-red" />
                                <span className="text-xs text-flame-red font-medium">
                                  AI Enhanced
                                </span>
                              </div>
                              {editedVersions.length > 1 && (
                                <Badge variant="outline" className="text-xs">
                                  {editedVersions.length} versions
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                        {/* Selected indicator and overlay*/}
                        {selectedForEdit.has(image.id) && (
                          <>
                            <img
                              src="/selected2.avif"
                              alt="Selected"
                              className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10 drop-shadow-lg"
                            />

                            <div
                              id="selected-overlay"
                              className="absolute inset-0 bg-red-200/20 z-2"
                            />
                          </>
                        )}

                        {/* Remove Button */}
                        <Button
                          onClick={() => removeImage(image.id)}
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 bg-white/90 hover:bg-white"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              );
            })()}
          </div>
        )}

        {/* Edit Results Section - Always show when results exist */}
        {results.length > 0 && (
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h3 className="text-h2 font-semibold text-graphite-90">
                  AI Enhanced Images ({results.length}
                  {retryingCards.size > 0 ? ` + ${retryingCards.size} creating` : ""})
                </h3>
                {images.length === 0 && results.length > 0 && (
                  <p className="text-sm text-graphite-60 mt-1">
                    Original images removed, enhanced versions preserved
                  </p>
                )}
                {retryingCards.size > 0 && (
                  <p className="text-sm text-blue-600 mt-1">
                    Creating {retryingCards.size} new version
                    {retryingCards.size > 1 ? "s" : ""}...
                  </p>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-graphite-60">
                <Sparkles className="h-4 w-4 text-flame-red" />
                <span>Swipe to compare before/after</span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...results].reverse().map((result) => {
                // Calculate version info for this result
                const sameOriginalResults = results
                  .filter((r) => r.originalId === result.originalId && r.success)
                  .sort((a, b) => {
                    // Sort by creation time (extract from ID or use index)
                    const aTime = a.id.includes("_") ? parseInt(a.id.split("_").pop() || "0") : 0;
                    const bTime = b.id.includes("_") ? parseInt(b.id.split("_").pop() || "0") : 0;
                    return aTime - bTime;
                  });

                const versionInfo =
                  sameOriginalResults.length > 1
                    ? {
                        current: sameOriginalResults.findIndex((r) => r.id === result.id) + 1,
                        total: sameOriginalResults.length,
                      }
                    : undefined;

                return (
                  <div key={result.id} className="animate-fade-in">
                    <EditCard
                      result={result}
                      originalImage={images.find((img) => img.id === result.originalId)}
                      onDownload={handleDownload}
                      onRetry={handleRetry}
                      onRemove={removeEditedImage}
                      versionInfo={versionInfo}
                      isRetrying={retryingCards.has(result.originalId)}
                    />
                  </div>
                );
              })}

              {/* Show placeholder cards for images being retried */}
              {Array.from(retryingCards).map((originalId) => {
                const originalImage = images.find((img) => img.id === originalId);
                const existingResult = results.find((r) => r.originalId === originalId);
                const retryKey = Object.keys(progress).find((key) =>
                  key.startsWith(`${originalId}_retry_`)
                );
                const retryProgress = retryKey ? progress[retryKey] : undefined;

                return (
                  <div
                    key={`retry-${originalId}`}
                    className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse"
                  >
                    <div className="relative aspect-[7/10]">
                      {/* Show original image with processing overlay */}
                      <img
                        src={
                          existingResult?.originalImagePreview ||
                          originalImage?.preview ||
                          "/placeholder.svg"
                        }
                        alt="Creating new version..."
                        className="w-full h-full object-cover opacity-50"
                      />
                      <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                        <div className="bg-white/95 backdrop-blur-sm rounded-lg p-4 text-center">
                          <RefreshCw className="h-8 w-8 mx-auto mb-2 text-blue-600 animate-spin" />
                          <div className="text-sm font-medium text-blue-900">
                            Creating New Version
                          </div>
                          {retryProgress && (
                            <div className="text-xs text-blue-700 mt-1">{retryProgress.status}</div>
                          )}
                        </div>
                      </div>

                      {/* New version indicator */}
                      <div className="absolute top-3 right-3">
                        <Badge className="bg-blue-600 text-white">
                          <Sparkles className="h-3 w-3 mr-1" />
                          New Version
                        </Badge>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                      <div className="h-3 bg-gray-100 rounded w-3/4 animate-pulse"></div>

                      {retryProgress && (
                        <div className="mt-3">
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-600">Processing</span>
                            <span className="text-blue-600 font-medium">
                              {Math.round(retryProgress.progress)}%
                            </span>
                          </div>
                          <Progress value={retryProgress.progress} className="h-2" />
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Upload prompt when no original images but have edited images */}
        {images.length === 0 && results.length > 0 && (
          <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center mt-0.5">
                <Sparkles className="text-white text-xs" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900 mb-1">
                  Your AI-enhanced images are preserved
                </h4>
                <p className="text-sm text-blue-700 mb-3">
                  You've removed the original images but your AI-enhanced versions are still here.
                  Upload new images to create more enhancements.
                </p>
                <div
                  {...getRootProps()}
                  className="p-4 border-2 border-dashed border-blue-300 rounded-lg text-center cursor-pointer hover:border-blue-400 hover:bg-blue-100/50 transition-colors"
                >
                  <input {...getInputProps()} />
                  <FileUp className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                  <p className="text-sm text-blue-700">Upload more images to enhance</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {images.length === 0 && results.length === 0 && isStorageReady && (
          <div className="text-center py-12">
            <Wand2 className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-h2 font-semibold text-graphite-90 mb-2">
              Ready to Edit Your Images
            </h3>
            <p className="text-body-md text-graphite-60 mb-6 max-w-md mx-auto">
              Upload up to {MAX_IMAGES} images and use AI to enhance them for your dating profile.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
