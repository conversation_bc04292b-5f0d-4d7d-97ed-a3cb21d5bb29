import { Link } from "@tanstack/react-router";
import {
  Heart,
  Sparkles,
  Star,
  Zap,
  Shield,
  Target,
  Flame,
  MessageSquare,
  Camera,
} from "lucide-react";
import { AnimatedPhoneMockup } from "@/components/animated-phone-mockup";
import { StaticPhoneMockup } from "@/components/static-phone-mockup";
import { But<PERSON> } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useEffect, useState } from "react";

export function LandingPage() {
  return (
    <div className="bg-cloud-white text-graphite-90">
      <HeroSection />
      <UnifiedParallaxJourney />
      <Footer />
    </div>
  );
}

//DO NOT CHANGE HERO SECTION EVER, IF YOU DO, YOU WILL BE FIRED
function HeroSection() {
  return (
    <section className="w-full bg-gradient-hero">
      <div className="container mx-auto grid min-h-screen items-center gap-8 px-4 md:grid-cols-2 md:px-6 lg:gap-16">
        <div className="space-y-6">
          <h1 className="text-h1-mobile md:text-h1 text-balance">
            Turn Your Dating Profile <br /> Into a Match Magnet
          </h1>
          <p className="text-body-lg text-graphite-60 max-w-lg">
            AI analyzes 50+ factors to optimize your photos and bio. Get 3x more quality matches in 30 days or your money back.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button asChild size="lg" variant="primary" className="shadow-lg">
              <Link to="/welcome">Start Free Analysis</Link>
            </Button>
            <div className="flex items-center gap-2 text-sm text-graphite-60">
              <div className="flex -space-x-1">
                <div className="w-6 h-6 rounded-full bg-gradient-primary flex items-center justify-center text-xs text-white font-semibold">4.9</div>
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
              </div>
              <span>50,000+ profiles optimized</span>
            </div>
          </div>
        </div>
        <div className="relative h-full items-center justify-center md:flex">
          <div className="relative flex items-center justify-center">
            <StaticPhoneMockup rotation={-8} />
            <AnimatedPhoneMockup rotation={8} className="relative -ml-4 md:-ml-8" />
          </div>
        </div>
      </div>
    </section>
  );
}

// Unified continuous parallax journey
function UnifiedParallaxJourney() {
  const [scrollY, setScrollY] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const containerRef = useScrollAnimation<HTMLDivElement>({ threshold: 0.01 });

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      const heroHeight = window.innerHeight;
      const relativeScroll = Math.max(0, scrolled - heroHeight);
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight - heroHeight;
      const progress = Math.min(1, relativeScroll / maxScroll);
      
      setScrollY(relativeScroll);
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Progressive perspective values based on scroll
  const perspective = 800 + scrollProgress * 400;
  const rotateX = 45 - scrollProgress * 45;
  const translateZ = -500 + scrollProgress * 500;

  return (
    <div ref={containerRef.ref} className="relative" style={{ height: '300vh' }}>
      {/* Fixed viewport container */}
      <div className="fixed-screen bg-gradient-to-b from-graphite-90 via-graphite-60 to-flame-red/20">
        {/* Background parallax layers moving at different speeds */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Deep space layer */}
          <div 
            className="absolute inset-0"
            style={{
              transform: `translateY(${scrollY * 0.2}px) scale(1.5)`,
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20" />
            {/* Floating particles - reduced for performance */}
            {[...Array(12)].map((_, i) => (
              <div
                key={i}
                className="absolute rounded-full bg-white/10 motion-safe:animate-pulse"
                style={{
                  width: `${Math.random() * 3 + 2}px`,
                  height: `${Math.random() * 3 + 2}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  transform: `translateY(${scrollY * (0.2 + Math.random() * 0.4)}px)`,
                  animationDelay: `${Math.random() * 2}s`,
                }}
              />
            ))}
          </div>

          {/* Mid layer - grid - simplified for mobile */}
          <div
            className="absolute inset-0 hidden md:block"
            style={{
              backgroundImage: 'linear-gradient(rgba(255,88,81,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,88,81,0.1) 1px, transparent 1px)',
              backgroundSize: '50px 50px',
              transform: `perspective(${perspective}px) rotateX(${rotateX}deg) translateZ(${translateZ * 0.5}px) translateY(${scrollY * 0.5}px)`,
              transformOrigin: 'center bottom',
              opacity: 0.5 + scrollProgress * 0.5,
            }}
          />
        </div>

        {/* Main content container with continuous flow */}
        <div className="relative z-10 h-full flex items-center justify-center">
          <div className="container mx-auto px-4">
            {/* Scene 1: The Problem (0-25% scroll) */}
            <div
              className="absolute inset-0 flex items-center justify-center z-10"
              style={{
                opacity: scrollProgress < 0.25 ? Math.pow(1 - scrollProgress * 4, 1.5) : 0,
                transform: `
                  translateX(${scrollProgress < 0.15 ? 0 : scrollProgress * -200}px)
                  translateY(${scrollProgress * -50}px) 
                  scale(${1 - scrollProgress * 0.1})
                `,
                pointerEvents: scrollProgress < 0.25 ? 'auto' : 'none',
              }}
            >
              <div className="text-center text-cloud-white max-w-4xl px-8 py-16">
                <Heart className="w-20 h-20 mx-auto mb-12 text-flame-red animate-pulse mt-16" />
                <h2 className="text-4xl md:text-6xl font-bold mb-10 text-balance">
                  73% of Dating Profiles Get Zero Matches
                </h2>
                <p className="text-lg md:text-xl opacity-90 mb-16 max-w-2xl mx-auto leading-relaxed">
                  Most people struggle with dating apps because they don't know what actually works. Your photos and bio are everything.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 mt-8 md:mt-12">
                  {[
                    { value: "73%", label: "Get zero matches" },
                    { value: "4,000", label: "Swipes before 1 date" },
                    { value: "3 sec", label: "To judge your profile" },
                  ].map((stat, i) => (
                    <div
                      key={i}
                      className="bg-white/10 backdrop-blur-md rounded-xl md:rounded-2xl p-4 md:p-6 text-center"
                      style={{
                        transform: `translateY(${scrollProgress * (i - 1) * 50}px)`,
                        opacity: 1 - scrollProgress * 2,
                      }}
                    >
                      <div className="text-2xl md:text-3xl font-bold text-flame-red">{stat.value}</div>
                      <div className="text-xs md:text-sm opacity-80 mt-1">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Scene 2: The Solution (20-60% scroll) */}
            <div
              className="absolute inset-0 flex items-center justify-center z-20"
              style={{
                opacity: (() => {
                  if (scrollProgress < 0.2) return 0;
                  if (scrollProgress > 0.6) return 0;
                  if (scrollProgress < 0.27) return (scrollProgress - 0.2) * 14.3; // Slower fade in
                  if (scrollProgress > 0.53) return (0.6 - scrollProgress) * 14.3; // Slower fade out
                  return 1; // Peak visibility 27-53%
                })(),
                transform: `
                  translateX(${(() => {
                    if (scrollProgress < 0.27) return Math.max(0, 300 - (scrollProgress - 0.2) * 4285); // Smooth entry
                    if (scrollProgress > 0.53) return -(scrollProgress - 0.53) * 857; // Smooth exit
                    return 0; // Center position
                  })()}px)
                  translateY(${(scrollProgress - 0.4) * -30}px) 
                  scale(${0.98 + Math.sin((scrollProgress - 0.4) * Math.PI) * 0.02})
                `,
                transition: 'opacity 0.4s ease-in-out, transform 0.3s ease-out',
              }}
            >
              <div className="text-center text-cloud-white max-w-6xl px-8 py-32">
                <h2 className="text-4xl md:text-6xl font-bold mb-12 text-balance mt-24">
                  AI Optimizes Everything That Matters
                </h2>
                <p className="text-lg md:text-xl opacity-90 mb-24 max-w-2xl mx-auto leading-relaxed">
                  Our AI analyzes 50+ factors that affect your match rate and optimizes your profile automatically
                </p>
                
                {/* Improved feature cards - grid layout for better spacing */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 max-w-5xl mx-auto">
                  {[
                    {
                      icon: Camera,
                      title: "Photo Optimizer",
                      desc: "+127% more matches with optimized photo order",
                      stat: "+127%",
                      color: "from-blue-500 to-cyan-400",
                      iconBg: "bg-blue-500/20",
                    },
                    {
                      icon: MessageSquare,
                      title: "Bio Generator",
                      desc: "+89% more conversations with AI-written bios",
                      stat: "+89%",
                      color: "from-emerald-500 to-teal-400",
                      iconBg: "bg-emerald-500/20",
                    },
                    {
                      icon: Target,
                      title: "Match Analytics",
                      desc: "Personal insights to maximize your appeal",
                      stat: "4.9★",
                      color: "from-purple-500 to-pink-400",
                      iconBg: "bg-purple-500/20",
                    },
                  ].map((feature, i) => (
                    <div
                      key={i}
                      className="group"
                      style={{
                        animationDelay: `${i * 0.2}s`,
                      }}
                    >
                      <div className="relative bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-2xl md:rounded-3xl p-6 md:p-8 text-center border border-white/30 shadow-2xl group-hover:scale-105 transition-all duration-500 hover:shadow-flame-red/20 h-full">
                        {/* Gradient border effect */}
                        <div className={`absolute inset-0 rounded-2xl md:rounded-3xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl`}></div>
                        
                        {/* Header with icon and stat */}
                        <div className="flex items-center justify-between mb-6">
                          <div className={`p-4 rounded-xl ${feature.iconBg} backdrop-blur-sm border border-white/20`}>
                            <feature.icon className="w-8 h-8 md:w-10 md:h-10 text-white" />
                          </div>
                          <div className={`text-2xl md:text-3xl font-bold bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`}>
                            {feature.stat}
                          </div>
                        </div>
                        
                        {/* Content */}
                        <div className="space-y-4">
                          <h3 className="text-xl md:text-2xl font-bold text-white">{feature.title}</h3>
                          <p className="text-base md:text-lg text-white/90 leading-relaxed">{feature.desc}</p>
                        </div>
                        
                        {/* Bottom accent line */}
                        <div className={`mt-8 h-1 w-full bg-gradient-to-r ${feature.color} rounded-full opacity-80`}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Scene 3: Clean, Readable CTA (55-100% scroll) */}
            <div
              className="absolute inset-0 flex items-center justify-center z-30"
              style={{
                opacity: (() => {
                  if (scrollProgress < 0.57) return 0;
                  if (scrollProgress < 0.67) return (scrollProgress - 0.57) * 10; // Fade in over 10%
                  return 1; // Full visibility from 67-100%
                })(),
                transform: `
                  translateX(${scrollProgress < 0.67 ? Math.max(0, 400 - (scrollProgress - 0.57) * 4000) : 0}px)
                  translateY(${Math.max(0, (0.7 - scrollProgress) * 50)}px) 
                  scale(${Math.min(1, 0.85 + (scrollProgress - 0.55) * 3)})
                `,
                transition: 'opacity 0.5s ease-in-out, transform 0.4s ease-out',
              }}
            >
              <div className="text-center max-w-3xl mx-auto px-9 py-12">
                {/* Solid high-contrast background */}
                <div className="bg-graphite-90 rounded-3xl border-2 border-flame-red/30 shadow-2xl p-12 md:p-18">
                  {/* Clear, readable headline - solid color */}
                  <h2 className="text-3xl md:text-5xl font-bold mb-9 text-cloud-white text-balance">
                    Get 3x More Matches in 30 Days
                  </h2>
                  
                  {/* Readable subtext */}
                  <p className="text-lg md:text-xl mb-12 text-cloud-white/90 leading-relaxed">
                    Join 50,000+ people who transformed their dating success with AI optimization
                  </p>
                  
                  {/* Trust indicators - clean and visible */}
                  <div className="flex flex-wrap justify-center items-center gap-6 mb-9 text-cloud-white/80 text-base">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span>Free to start</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span>No credit card</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span>2-minute setup</span>
                    </div>
                  </div>
                  
                  {/* Clear, prominent CTA */}
                  <Button
                    asChild
                    size="lg"
                    className="text-lg px-12 py-6 bg-flame-red hover:bg-flame-red/90 text-white font-bold shadow-xl hover:shadow-2xl transition-all duration-300"
                  >
                    <Link to="/welcome">
                      Start Free Analysis Now
                      <Zap className="ml-3 w-6 h-6" />
                    </Link>
                  </Button>
                  
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll progress indicator */}
        <div className="fixed bottom-8 left-1/2 -translate-x-1/2 z-20">
          <div className="w-48 h-2 bg-white/20 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-flame-red to-sparks-pink transition-all duration-300"
              style={{ width: `${scrollProgress * 100}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}


function Footer() {
  return (
    <footer className="bg-graphite-90 text-cloud-white/80">
      <div className="container mx-auto px-4 md:px-6 py-12">
        {/* Clean Social Proof Section */}
        <div className="text-center mb-12">
          <div className="flex flex-wrap justify-center items-center gap-12 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-flame-red">50,000+</div>
              <div className="text-sm opacity-90">Profiles Optimized</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-flame-red">2M+</div>
              <div className="text-sm opacity-90">Matches Generated</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-flame-red">4.9★</div>
              <div className="text-sm opacity-90">User Rating</div>
            </div>
          </div>
          <blockquote className="text-xl italic text-cloud-white max-w-3xl mx-auto leading-relaxed">
            "TinderOptimizer completely transformed my dating life. I went from zero matches to having conversations every day!"
            <footer className="text-sm text-flame-red mt-3 font-medium">- Sarah M., verified user</footer>
          </blockquote>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          <div>
            <h4 className="font-semibold text-lg text-cloud-white mb-3">
              TinderOptimizer
            </h4>
            <p className="text-sm opacity-90 mb-4">AI-powered dating profile optimization for 3x more matches.</p>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-success-green" />
              <span className="text-xs opacity-80">Privacy Protected & Secure</span>
            </div>
          </div>
          <div>
            <h5 className="font-semibold text-cloud-white mb-3">Features</h5>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/image-analyzer" className="hover:text-flame-red transition-colors">
                  Photo Optimizer
                </Link>
              </li>
              <li>
                <Link to="/bio-analyzer" className="hover:text-flame-red transition-colors">
                  Bio Generator
                </Link>
              </li>
              <li>
                <Link to="/welcome" className="hover:text-flame-red transition-colors">
                  Free Analysis
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h5 className="font-semibold text-cloud-white mb-3">Support</h5>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="hover:text-flame-red transition-colors">
                  Help Center
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-flame-red transition-colors">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-flame-red transition-colors">
                  Terms of Service
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-graphite-60/30 mt-12 pt-8 text-center text-sm">
          <p className="opacity-80">
            &copy; {new Date().getFullYear()} TinderOptimizer. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
