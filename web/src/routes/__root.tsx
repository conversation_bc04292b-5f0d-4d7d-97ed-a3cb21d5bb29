import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/tanstack-react-start";
import {
  createRootRoute,
  HeadContent,
  Outlet,
  Scripts,
} from "@tanstack/react-router";
/// <reference types="vite/client" />
import type { ReactNode } from "react";
import appCss from "@/globals.css?url";
// //@ts-ignore
import { cn } from "@/lib/utils";
import { Navbar } from "@/components/Navbar";

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      {
        title: "Tinder AI Profile Optimizer",
      },
    ],
    links: [
      { rel: "stylesheet", href: appCss },
      {
        rel: "icon",
        href: "/tinderOptimizer32x32.avif",
      },
    ],
  }),
  component: RootComponent,
});

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  );
}

function RootDocument({ children }: Readonly<{ children: ReactNode }>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <head>
          <HeadContent />
        </head>
        <body
          className={cn("min-h-screen bg-background font-sans antialiased")}
        >
          <Navbar />
          <main className="flex-1">
            {children}
          </main>
          <Scripts />
        </body>
      </html>
    </ClerkProvider>
  );
}
