import { SignedIn, SignedOut, SignIn, SignUp } from "@clerk/tanstack-react-start";
import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { ArrowLeft, Heart, Sparkles, Star } from "lucide-react";
import { useEffect, useState } from "react";
import { WizardContainer } from "@/components/@wizard/WizardContainer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export const Route = createFileRoute("/welcome")({
  component: WelcomeComponent,
});

function WelcomeComponent() {
  const [showSignIn, setShowSignIn] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
      <div className="w-full max-w-2xl lg:max-w-4xl">
        <SignedOut>
          {!showSignIn && !showSignUp && (
            <WelcomeCard
              onSignIn={() => setShowSignIn(true)}
              onSignUp={() => setShowSignUp(true)}
            />
          )}
          {showSignIn && <SignInCard onBack={() => setShowSignIn(false)} />}
          {showSignUp && <SignUpCard onBack={() => setShowSignUp(false)} />}
        </SignedOut>
        <SignedIn>
          <WizardRedirect />
        </SignedIn>
      </div>
    </div>
  );
}

interface WelcomeCardProps {
  onSignIn: () => void;
  onSignUp: () => void;
}

function WelcomeCard({ onSignIn, onSignUp }: WelcomeCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardContent className="p-6 lg:p-8">
        <div className="grid lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          {/* Left Column - Content */}
          <div className="text-center lg:text-left space-y-4">
            <div className="flex justify-center lg:justify-start mb-3">
              <div className="relative">
                <Heart className="w-10 h-10 text-flame-red fill-current" />
                <Sparkles className="w-3 h-3 text-sparks-pink absolute -top-0.5 -right-0.5" />
              </div>
            </div>

            <div className="space-y-2">
              <h1 className="text-2xl lg:text-3xl font-bold text-graphite-90">
                Welcome to TinderOP
              </h1>
              <p className="text-base text-graphite-60">
                AI-powered profile optimizer for better matches
              </p>
            </div>

            {/* Features - Compact */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-3 py-2">
              <div className="flex items-center space-x-2 text-sm text-graphite-60">
                <Star className="w-4 h-4 text-sparks-pink flex-shrink-0" />
                <span>AI photo & bio analysis</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-graphite-60">
                <Heart className="w-4 h-4 text-sparks-pink flex-shrink-0" />
                <span>Proven optimization tips</span>
              </div>
            </div>
          </div>

          {/* Right Column - Actions */}
          <div className="space-y-4">
            <div className="text-center lg:text-left">
              <p className="text-sm text-graphite-60 mb-4">Ready to get started?</p>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="primary" className="w-full h-11" onClick={onSignUp}>
                  Sign Up
                </Button>
                <Button variant="secondary" className="w-full h-11" onClick={onSignIn}>
                  Sign In
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface AuthCardProps {
  onBack: () => void;
}

function SignInCard({ onBack }: AuthCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="text-graphite-60 hover:text-graphite-90"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1 flex justify-center">
            <Heart className="w-8 h-8 text-flame-red fill-current" />
          </div>
          <div className="w-10" />
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-graphite-90 mb-1">Sign In</h2>
          <p className="text-sm text-graphite-60">Welcome back! Continue optimizing your profile</p>
        </div>

        <div className="flex justify-center">
          <SignIn
            routing="hash"
            signUpUrl="/welcome"
            afterSignInUrl="/dashboard"
            appearance={{
              elements: {
                rootBox: "w-full",
                card: "shadow-none border-0 bg-transparent",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                socialButtonsBlockButton:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                formButtonPrimary:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                footerActionLink: "text-flame-red hover:text-flame-red/80",
              },
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function SignUpCard({ onBack }: AuthCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="text-graphite-60 hover:text-graphite-90"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1 flex justify-center">
            <Heart className="w-8 h-8 text-flame-red fill-current" />
          </div>
          <div className="w-10" />
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-graphite-90 mb-1">Sign Up</h2>
          <p className="text-sm text-graphite-60">
            Create your account and start getting better matches
          </p>
        </div>

        <div className="flex justify-center">
          <SignUp
            routing="hash"
            signInUrl="/welcome"
            afterSignUpUrl="/dashboard"
            appearance={{
              elements: {
                rootBox: "w-full",
                card: "shadow-none border-0 bg-transparent",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                socialButtonsBlockButton:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                formButtonPrimary:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                footerActionLink: "text-flame-red hover:text-flame-red/80",
              },
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function WizardRedirect() {
  const [showWizard, setShowWizard] = useState(false);

  useEffect(() => {
    // Small delay to ensure smooth transition
    const timer = setTimeout(() => {
      setShowWizard(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!showWizard) {
    return (
      <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
        <CardContent className="p-6 text-center">
          <div className="animate-pulse">
            <Heart className="w-10 h-10 text-flame-red fill-current mx-auto mb-3" />
            <p className="text-graphite-60">Loading your profile setup...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <WizardContainer />;
}
