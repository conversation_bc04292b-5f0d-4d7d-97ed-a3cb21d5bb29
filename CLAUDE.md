# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Quick Commands

```bash
cd web                                    # Navigate to the main app directory
bun run dev                               # Start development server (Vite)
bun run build                             # Build for production
bun run preview                           # Preview production build
bun run typecheck                         # Run TypeScript type checking
bun run deploy                            # Deploy to Cloudflare Workers
bun run lint                              # Check code quality with Biome
bun run lint:fix                          # Fix auto-fixable issues with Biome
bun run format                            # Format code with Biome
```

## 🛠️ Tech Stack

- **Frontend Framework**: TanStack Start with React 19 and TypeScript
- **Build Tool**: Vite Rolldown
- **Routing**: TanStack Router with file-based routing
- **State Management**: Zustand with persistence middleware
- **Styling**: Tailwind CSS with custom design tokens
- **UI Components**: Radix UI primitives with custom styling
- **AI Integration**: @openrouter/ai-sdk-provider (image/bio analysis)
- **Forms**: TanStack Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Development**: Strict TypeScript, Biome for linting/formatting
- **Deployment**: Cloudflare Workers via Wrangler CLI
- **AI Providers**: OpenRouter integration

# MUST DEV GUIDELINE: FOLLOW RULES
1. Both Component, File Name and Main Function MUST have the same name, the name must be descriptive and must reflect their purpose. 
  FOR EXAMPLE: WizardStep1_Info.tsx (That's the name that the file must have), WizardStep1_Info (That's the name of the main function).

### Development Guidelines
1. Use TanStack start docs via Context7 MCP for latest routing patterns
2. Functions should do one thing with descriptive names
3. Keep structure and code simple and readable
4. Prefer editing existing files over creating new ones 

## 📁 Project Architecture

The project follows a clean React SPA structure in the `web/` directory:

```
web/                         # Main React SPA (primary development)
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Radix-based design system components
│   │   └── *-mockup.tsx    # Phone UI mockups
│   ├── routes/             # TanStack Router file-based routing
│   │   ├── __root.tsx      # Root layout with Clerk provider
│   │   ├── _authed/        # Protected routes
│   │   └── index.tsx       # Landing page
│   ├── stores/             # Zustand state management
│   │   └── imageStore.ts   # Global image state (original & enhanced)
│   ├── lib/                # Core business logic
│   │   ├── advanced/       # Advanced AI analysis features
│   │   └── *.ts            # Services, utilities, storage
│   ├── hooks/              # Custom React hooks
│   ├── types/              # TypeScript interfaces
│   ├── main.tsx            # App entry point
│   └── globals.css         # Global styles & CSS variables
├── public/                 # Static assets
chrome-extension/           # Browser extension for dating apps
app/                        # Future app directory (placeholder)
```

## 🎨 Design System

### Color Palette
The app uses a Tinder-inspired color scheme with custom design tokens:
- **Primary**: Flame Red (`#FF5851`) with gradient variants
- **Secondary**: Sparks Pink (`#FF8A80`)
- **Neutral**: Graphite shades (`#1A1A1A`, `#666666`)
- **Background**: Cloud White (`#FFFFFF`)

### Typography Scale
Custom font sizes with consistent line heights:
- `display-1`: 56px/64px (40px/48px mobile)
- `h1`: 40px/48px (32px/40px mobile)  
- `h2`: 32px/40px (24px/32px mobile)
- `body-lg`: 18px/28px
- `body-md`: 16px/24px
- `caption`: 14px/20px

### Component Architecture
- All UI components extend Radix UI primitives
- Consistent use of `class-variance-authority` for component variants
- Tailwind utilities with custom design tokens
- `@/` path alias configured for clean imports

## 🧩 Key Patterns

### Routing Structure (TanStack Router)
- File-based routing in `src/routes/` directory
- Protected routes under `_authed/` directory  
- Layout nesting via `<Outlet />` pattern from `__root.tsx`
- Error boundaries and 404 handling built-in

### Component Composition
```typescript
// Standard component pattern with Radix + CVA
export function ComponentName({ variant, className, ...props }: Props) {
  return (
    <RadixPrimitive 
      className={cn(componentVariants({ variant }), className)}
      {...props}
    />
  );
}
```

### AI Integration
- OpenAI integration via `@openrouter/ai-sdk-provider`
- OpenRouter as additional AI provider via `@openrouter/ai-sdk-provider`
- Multi-step analysis agents with progress tracking
- Streaming responses for real-time analysis
- Service classes for business logic (`AnalysisService`)

### State Management
- **Zustand**: Global state management for image storage
  - Centralized store for original and AI-enhanced images
  - Automatic persistence with IndexedDB
  - DevTools integration for debugging
- React hooks for local component state
- TanStack Form for complex form handling
- IndexedDB for image blob storage
- localStorage for session management

### Storage Architecture
The app uses a layered storage approach for handling images:

1. **Zustand Store** (`/stores/imageStore.ts`)
   - Global state management for all images
   - Separate arrays for original and enhanced images
   - Automatic session management
   - Helper hooks for accessing image data

2. **IndexedDB Layer** (`/lib/storage.ts`)
   - Persistent storage for image blobs
   - Session-based organization
   - Automatic cleanup of old images (24h)
   - Support for storing original images with enhanced versions

3. **Key Features**:
   - **Original Images**: User-uploaded photos for analysis
   - **Enhanced Images**: AI-processed versions with edit history
   - **Session Management**: Automatic session tracking
   - **Memory Management**: Automatic cleanup of preview URLs
   - **Cross-Page Sharing**: Images accessible from any page

4. **Usage Pattern**:
   ```typescript
   // Initialize store on app start
   await useImageStore.getState().initializeStore();
   
   // Add images
   const imageIds = await useImageStore.getState().addOriginalImages(files);
   
   // Access images
   const { originalImages, enhancedImages } = useImageStore();
   ```
- Primarily uses React hooks (useState, useEffect)
- TanStack Form for complex form handling
- Local storage for image and analysis data persistence
- No global state management library currently used

## 🔧 Development Workflow

### File Organization
- Components are co-located by feature/page when possible
- Shared UI components in `components/ui/`
- Utilities in `lib/utils.ts`
- Global styles use CSS custom properties for theming

### TypeScript Configuration
- Strict mode enabled with additional checks
- Path aliases: `@/*` maps to `src/*`
- ES2020 target with modern module resolution

### Build & Deployment
- Vite handles bundling and HMR (using experimental `rolldown-vite`)
- PostCSS processes Tailwind CSS
- Cloudflare Workers deployment via Wrangler
- Production builds optimized for edge deployment

### Code Quality
- Biome for linting and formatting (replacing ESLint/Prettier)
- 2-space indentation, 100-character line width
- Strict TypeScript configuration with additional checks
- Import organization and type imports enforced

## 🎯 Application Context

TinderOP is a dating profile optimization tool that provides:
1. **Image Analysis**: AI-powered photo scoring and recommendations
2. **Bio Enhancement**: Bio writing assistance and optimization  
3. **Multi-step Analysis**: Progressive analysis with callback-based architecture

The app focuses on improving dating success through AI-driven recommendations and has a clean, modern interface that mirrors dating app aesthetics.

The tinder images have an aspect ratio of 7:10

### Current Development Status
- **Main Branch**: `main` (production-ready)
- **Current Branch**: `gpt1-image-edit` (active development)
- **Architecture**: Privacy-focused with client-side processing
- **Authentication**: Clerk integration configured in `__root.tsx` (ready to enable)
- **Chrome Extension**: Integrated for dating app assistance
- **Testing**: No formal testing framework currently configured

## 📝 Git Commit Conventions

This project follows [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0-beta.2/) specification.

### Commit Message Format
```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

### Primary Commit Types
- `feat:` - Introduces a new feature (correlates with MINOR in semantic versioning)
- `fix:` - Patches a bug (correlates with PATCH in semantic versioning)
- `BREAKING CHANGE:` - Introduces an API-breaking change (correlates with MAJOR version)

### Additional Types
- `chore:` - Maintenance tasks, dependency updates
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, semicolons, etc.)
- `refactor:` - Code refactoring without changing functionality
- `perf:` - Performance improvements
- `test:` - Adding or updating tests
- `build:` - Changes to build system or dependencies
- `ci:` - Changes to CI configuration

### Examples
```bash
# Basic feature
feat: add dark mode toggle to settings

# Feature with scope
feat(bio-analyzer): implement multi-step AI analysis

# Bug fix
fix: resolve image upload validation error

# Breaking change
feat!: restructure API response format

BREAKING CHANGE: API now returns data in nested object structure

# Chore with scope
chore(deps): update React to v19

# Documentation
docs: add deployment instructions to README
```

### Commit Message Guidelines
- Use imperative mood ("add" not "added" or "adds")
- Keep description under 50 characters
- Use lowercase for type and description
- Include scope in parentheses when relevant
- Add body for complex changes
- Reference issues in footer when applicable